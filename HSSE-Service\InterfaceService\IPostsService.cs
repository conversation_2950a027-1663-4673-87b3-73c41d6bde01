﻿using HSSE_ModelDto.ModelDto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace HSSE_Service.InterfaceService
{
    public interface IPostsService
    {
        ApiResponseDto<MstPostDto> InsertOrUpdatePost(MstPostDto postDto);
        ApiResponseDto<List<MstPostDto>> GetPosts(int? userId, bool? showOnlyMine);
        ApiResponseDto<dynamic> InsertLikestoPost(MstLikesConfigDto postDto);
        ApiResponseDto<object> InsertOrUpdatePostCategory(MstPostCategoryDto dto);
        ApiResponseDto<object> GetPostCategories();
        ApiResponseDto<object> GetPostCategoryById(int catId);
        ApiResponseDto<object> DeletePostCategory(int catId);
        ApiResponseDto<MstPostDto> GetPostDetailsById(int postId);
        ApiResponseDto<List<MstPostDto>> GetPostDetails();
        ApiResponseDto<object> InsertOrUpdatePostComment(MstPostCommentDto dto);
        ApiResponseDto<List<MstPostCommentDto>> GetPostComment(int postId);
        ApiResponseDto<object> InsertOrUpdateFollowupPost(MstFollowupPostDto dto);
        ApiResponseDto<List<MstPostDto>> GetAssignedPosts(int? userId, int? status = null);
        ApiResponseDto<string> UpdateFollowupStatus(FollowupStatusUpdateRequestDto request);
        ApiResponseDto<object> GetAssignedUser(int postId);
        ApiResponseDto<object> DeletePostById(int postId, int deletedBy);
    }
}
