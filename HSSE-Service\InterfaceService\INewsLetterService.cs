using HSSE_ModelDto.ModelDto;

namespace HSSE_Service.InterfaceService
{
    public interface INewsLetterService
    {
        ApiResponseDto<object> InsertOrUpdateNewsLetter(MstNewsletterDto newsLetterDto);
        ApiResponseDto<object> GetNewsLettersByUserId(int userId);
        ApiResponseDto<object> GetNewsLettersByFacilityId(int facilityId, int userId);
        ApiResponseDto<object> DeleteNewsLetter(int newsLetterId);
        ApiResponseDto<object> ToggleNewsLetterActivation(int newsLetterId);
        ApiResponseDto<object> GetNewsLettersById(int newsletterId);
    }
}