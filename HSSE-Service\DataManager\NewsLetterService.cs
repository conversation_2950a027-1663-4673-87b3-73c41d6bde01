using AutoMapper;
using HSSE_Domain.Models;
using HSSE_ModelDto.ModelDto;
using HSSE_Service.Constants;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;

namespace HSSE_Service.DataManager
{
    public class NewsLetterService : INewsLetterService
    {
        private readonly HsseDbLatestContext _context;
        private readonly IMapper _mapper;

        public NewsLetterService(HsseDbLatestContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public ApiResponseDto<object> InsertOrUpdateNewsLetter(MstNewsletterDto dto)
        {
            try
            {
                MstNewsletter entity;
                bool isUpdate = dto.NewsletterId > 0;
                if (isUpdate)
                {
                    entity = _context.MstNewsletters.Find(dto.NewsletterId);
                    if (entity == null)
                    {
                        return ApiResponseDto<object>.ErrorResponse(AppMessages.NewsletterNotFound, StatusCodes.Status404NotFound);
                    }
                    string existingThumbnailPath = entity.ThumbnailPath;

                    _mapper.Map(dto, entity);
                    // Restore the original ThumbnailPath if the new value is null
                    if (dto.ThumbnailPath == null)
                    {
                        entity.ThumbnailPath = existingThumbnailPath;
                    }

                    _context.MstNewsletters.Update(entity);
                }
                else
                {
                    entity = _mapper.Map<MstNewsletter>(dto);
                    _context.MstNewsletters.Add(entity);
                }
                _context.SaveChanges();
                return ApiResponseDto<object>.SuccessResponse(isUpdate ? AppMessages.NewsletterUpdatedSuccessfully : AppMessages.NewsletterCreatedSuccessfully, StatusCodes.Status200OK, _mapper.Map<MstNewsletterDto>(entity));
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public ApiResponseDto<object> GetNewsLettersByUserId(int userId)
        {
            try
            {
                var newsletters = _context.MstNewsletters.Where(x => x.CreatedBy == userId).ToList();
                var dtos = _mapper.Map<List<MstNewsletterDto>>(newsletters);
                return ApiResponseDto<object>.SuccessResponse(AppMessages.NewsletterRetrieved, StatusCodes.Status200OK, dtos);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }
        public ApiResponseDto<object> GetNewsLettersById(int newsletterId)
        {
            try
            {
                var newsletters = _context.MstNewsletters.FirstOrDefault(x => x.NewsletterId == newsletterId);
                var dtos = _mapper.Map<MstNewsletterDto>(newsletters);
                return ApiResponseDto<object>.SuccessResponse(AppMessages.NewsletterRetrieved, StatusCodes.Status200OK, dtos);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }
        public ApiResponseDto<object> GetNewsLettersByFacilityId(int facilityId, int userId)
        {
            try
            {
                DateTime date = DateTime.Now;
                var newsletters = _context.MstNewsletters
       .Where(x =>
           x.FacilityId == facilityId &&
           x.CreatedBy != userId &&
           x.IsActive &&
           (
               !x.ScheduleAt.HasValue || // ScheduleAt is null
               x.ScheduleAt.Value.Date == date.Date // or matches given date
           )
       )
       .ToList();

                var dtos = _mapper.Map<List<MstNewsletterDto>>(newsletters);
                return ApiResponseDto<object>.SuccessResponse(AppMessages.NewsletterRetrieved, StatusCodes.Status200OK, dtos);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public ApiResponseDto<object> DeleteNewsLetter(int newsLetterId)
        {
            try
            {
                var entity = _context.MstNewsletters.Find(newsLetterId);
                if (entity == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.NewsletterNotFound, StatusCodes.Status404NotFound);
                }
                _context.MstNewsletters.Remove(entity);
                _context.SaveChanges();
                return ApiResponseDto<object>.SuccessResponse(AppMessages.NewsletterDeletedSuccessfully, StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public ApiResponseDto<object> ToggleNewsLetterActivation(int newsLetterId)
        {
            try
            {
                var entity = _context.MstNewsletters.Find(newsLetterId);
                if (entity == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.NewsletterNotFound, StatusCodes.Status404NotFound);
                }
                entity.IsActive = !entity.IsActive;
                _context.SaveChanges();
                var message = entity.IsActive ? AppMessages.NewsletterActivatedSuccessfully : AppMessages.NewsletterDeactivatedSuccessfully;
                return ApiResponseDto<object>.SuccessResponse(message, StatusCodes.Status200OK, entity.IsActive);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }
    }
} 