﻿using AutoMapper;
using HSSE_Domain.Models;
using HSSE_ModelDto.Enum;
using HSSE_ModelDto.ModelDto;
using HSSE_Service.Constants;
using HSSE_Service.InterfaceService;
using HSSE_Service.NLog;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


namespace HSSE_Service.DataManager
{
    public class AnnouncementService : IAnnouncementService
    {
        private IMapper _mapper;
        readonly HsseDbLatestContext _context;
        private ILoggerManager _logger;
        private readonly HttpClient _httpClient;
        private IConfiguration _configuration;

        public AnnouncementService(IConfiguration configuration, HttpClient httpClient, HsseDbLatestContext dbContext, IMapper mapper, ILoggerManager logger)
        {
            _httpClient = httpClient;
            _context = dbContext;
            _mapper = mapper;
            _logger = logger;
            _configuration = configuration;
        }
        public async Task<ApiResponseDto<object>> InsertOrUpdateGroupAsync(MstGroupDto groupDto)
        {
            try
            {
                var group = _mapper.Map<MstGroup>(groupDto);
                if (group.GroupId == 0)
                {
                    _context.MstGroups.Add(group);
                }
                else
                {
                    _context.MstGroups.Update(group);
                }
                await _context.SaveChangesAsync();
                return ApiResponseDto<object>.SuccessResponse(AppMessages.GroupMemberSaved, StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                _logger.Error($"[InsertOrUpdateGroupAsync] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<object>> GetGroupsAsync()
        {
            try
            {
                var groups = await _context.MstGroups.ToListAsync();
                var groupDtos = _mapper.Map<List<MstGroupDto>>(groups);
                return ApiResponseDto<object>.SuccessResponse(AppMessages.GroupRetrieved, StatusCodes.Status200OK, groupDtos);
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetGroups] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<object>> GetGroupByIdAsync(int groupId)
        {
            try
            {
                var group = await _context.MstGroups.FindAsync(groupId);
                if (group == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.GroupNotFound, StatusCodes.Status404NotFound);
                }
                var groupDto = _mapper.Map<MstGroupDto>(group);
                return ApiResponseDto<object>.SuccessResponse(AppMessages.GroupRetrieved, StatusCodes.Status200OK, groupDto);
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetGroupById] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<object>> DeleteGroupAsync(int groupId)
        {
            try
            {
                var group = await _context.MstGroups.FindAsync(groupId);
                if (group == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.GroupNotFound, StatusCodes.Status404NotFound);
                }
                _context.MstGroups.Remove(group);
                await _context.SaveChangesAsync();
                return ApiResponseDto<object>.SuccessResponse(AppMessages.GroupMemberDeleted, StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                _logger.Error($"[DeleteGroup] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }


        public async Task<ApiResponseDto<object>> InsertOrUpdateGroupMemberAsync(MstGroupMemberDto groupMemberDto)
        {
            try
            {
                if (groupMemberDto.UserIds == null || groupMemberDto.UserIds.Count == 0)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.UserListCannotBeEmpty, StatusCodes.Status400BadRequest);
                }

                var groupId = groupMemberDto.GroupId;
                var oldGroupId = groupMemberDto.OldGroupId; // New field
                var incomingUserIds = new HashSet<int>(groupMemberDto.UserIds);

                // If oldGroupId is 0 or not provided, treat as new
                bool isNewInsert = oldGroupId == 0 || oldGroupId != groupId;

                // Fetch existing group members from the OLD group
                var existingGroupMembers = await _context.MstGroupMembers
                    .Where(x => x.GroupId == oldGroupId)
                    .ToListAsync();

                if (isNewInsert && existingGroupMembers.Count == 0)
                {
                    // ✅ CREATE SCENARIO
                    foreach (var userId in incomingUserIds)
                    {
                        _context.MstGroupMembers.Add(new MstGroupMember
                        {
                            GroupId = groupId,
                            UserId = userId,
                            CreatedAt = DateTime.UtcNow
                        });
                    }

                    await _context.SaveChangesAsync();

                    return ApiResponseDto<object>.SuccessResponse(
                        AppMessages.GroupMemberSaved,
                        StatusCodes.Status200OK,
                        new
                        {
                            GroupId = groupId,
                            AddedUserIds = incomingUserIds.ToList(),
                            RemovedUserIds = new List<int>()
                        });
                }
                else
                {
                    // ✏️ EDIT SCENARIO — may include group change
                    var existingUserIds = existingGroupMembers.Select(x => x.UserId).ToHashSet();

                    var userIdsToAdd = incomingUserIds.Except(existingUserIds).ToList();
                    var userIdsToRemove = existingUserIds.Except(incomingUserIds).ToList();

                    // Remove all users from old group if groupId has changed
                    if (oldGroupId != groupId)
                    {
                        _context.MstGroupMembers.RemoveRange(existingGroupMembers);

                        foreach (var userId in incomingUserIds)
                        {
                            _context.MstGroupMembers.Add(new MstGroupMember
                            {
                                GroupId = groupId,
                                UserId = userId,
                                CreatedAt = DateTime.UtcNow
                            });
                        }
                    }
                    else
                    {
                        // Same group, update only changes
                        foreach (var userId in userIdsToAdd)
                        {
                            _context.MstGroupMembers.Add(new MstGroupMember
                            {
                                GroupId = groupId,
                                UserId = userId,
                                CreatedAt = DateTime.UtcNow
                            });
                        }

                        if (userIdsToRemove.Any())
                        {
                            var membersToRemove = existingGroupMembers
                                .Where(x => userIdsToRemove.Contains(x.UserId))
                                .ToList();

                            _context.MstGroupMembers.RemoveRange(membersToRemove);
                        }
                    }

                    await _context.SaveChangesAsync();

                    return ApiResponseDto<object>.SuccessResponse(
                        AppMessages.GroupMemberUpdated,
                        StatusCodes.Status200OK,
                        new
                        {
                            GroupId = groupId,
                            AddedUserIds = userIdsToAdd,
                            RemovedUserIds = userIdsToRemove
                        });
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"[InsertOrUpdateGroupMemberAsync] Exception: {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<object>> InsertGroupMembersAsync(MstGroupMemberDto groupMemberDto)
        {
            try
            {
                if (groupMemberDto.UserIds == null || groupMemberDto.UserIds.Count == 0)
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.UserListCannotBeEmpty, StatusCodes.Status404NotFound);

                var exists = await _context.MstGroupMembers.AnyAsync(x => x.GroupId == groupMemberDto.GroupId);
                if (exists)
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.GroupAlreadyHasMembers, StatusCodes.Status404NotFound);

                foreach (var userId in groupMemberDto.UserIds)
                {
                    _context.MstGroupMembers.Add(new MstGroupMember
                    {
                        GroupId = groupMemberDto.GroupId,
                        UserId = userId,
                        CreatedAt = DateTime.UtcNow
                    });
                }

                await _context.SaveChangesAsync();

                return ApiResponseDto<object>.SuccessResponse(AppMessages.GroupMemberSaved, StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                _logger.Error($"[InsertGroupMembersAsync] Exception: {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<object>> UpdateGroupMembersAsync(MstGroupMemberDto groupMemberDto)
        {
            try
            {
                if (groupMemberDto.UserIds == null || groupMemberDto.UserIds.Count == 0)
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.UserListCannotBeEmpty, StatusCodes.Status404NotFound);

                var existingMembers = await _context.MstGroupMembers
                    .Where(x => x.GroupId == groupMemberDto.GroupId)
                    .ToListAsync();

                var existingUserIds = existingMembers.Select(x => x.UserId).ToHashSet();
                var incomingUserIds = groupMemberDto.UserIds.ToHashSet();

                var toAdd = incomingUserIds.Except(existingUserIds).ToList();
                var toRemove = existingUserIds.Except(incomingUserIds).ToList();

                foreach (var userId in toAdd)
                {
                    _context.MstGroupMembers.Add(new MstGroupMember
                    {
                        GroupId = groupMemberDto.GroupId,
                        UserId = userId,
                        CreatedAt = DateTime.UtcNow
                    });
                }

                if (toRemove.Any())
                {
                    var removeList = existingMembers
                        .Where(x => toRemove.Contains(x.UserId))
                        .ToList();

                    _context.MstGroupMembers.RemoveRange(removeList);
                }

                await _context.SaveChangesAsync();

                return ApiResponseDto<object>.SuccessResponse(AppMessages.GroupMemberUpdated, StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                _logger.Error($"[UpdateGroupMembersAsync] Exception: {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> GetGroupMembersAsync()
        {
            try
            {
                var groupMembers = await _context.MstGroupMembers
            .Include(g => g.Group) // Include navigation property
            .GroupBy(x => new { x.GroupId, x.Group.GroupName })
            .Select(g => new
            {
                GroupId = g.Key.GroupId,
                GroupName = g.Key.GroupName,
                UserCount = g.Count(),
                UserIds = g.Select(x => x.UserId).ToList(),
                CreatedAt = g.Min(x => x.CreatedAt)
            })
            .ToListAsync();

                return ApiResponseDto<object>.SuccessResponse(
                    AppMessages.GroupMemberRetrieved,
                    StatusCodes.Status200OK,
                    groupMembers);
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetGroupMembers] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<object>> GetGroupMembersByGroupIdAsync(int groupId)
        {
            try
            {
                var groupMembers = await _context.MstGroupMembers
            .Include(g => g.Group) .Where(x=>x.GroupId == groupId)
            .GroupBy(x => new { x.GroupId, x.Group.GroupName })
            .Select(g => new
            {
                GroupId = g.Key.GroupId,
                GroupName = g.Key.GroupName,
                UserCount = g.Count(),
                UserIds = g.Select(x => x.UserId).ToList(),
                CreatedAt = g.Min(x => x.CreatedAt)
            })
            .FirstOrDefaultAsync();

                return ApiResponseDto<object>.SuccessResponse(
                    AppMessages.GroupMemberRetrieved,
                    StatusCodes.Status200OK,
                    groupMembers);
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetGroupMembers] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> GetGroupMemberByIdAsync(int groupMemberId)
        {
            try
            {
                var groupMember = await _context.MstGroupMembers.FindAsync(groupMemberId);
                if (groupMember == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.GroupMemberNotFound, StatusCodes.Status404NotFound);
                }
                var groupMemberDto = _mapper.Map<MstGroupMemberDto>(groupMember);
                return ApiResponseDto<object>.SuccessResponse(AppMessages.GroupMemberRetrieved, StatusCodes.Status200OK, groupMemberDto);
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetGroupMemberById] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<object>> DeleteAllGroupMembersByGroupIdAsync(int groupId)
        {
            try
            {
                if (groupId <= 0)
                {
                    return ApiResponseDto<object>.ErrorResponse("Invalid Group ID.", StatusCodes.Status400BadRequest);
                }

                var groupMembers = await _context.MstGroupMembers
                    .Where(x => x.GroupId == groupId)
                    .ToListAsync();

                if (!groupMembers.Any())
                {
                    return ApiResponseDto<object>.ErrorResponse("No members found for the specified group.", StatusCodes.Status404NotFound);
                }

                _context.MstGroupMembers.RemoveRange(groupMembers);
                await _context.SaveChangesAsync();

                return ApiResponseDto<object>.SuccessResponse(
                    $"All members removed from Group ID {groupId}.",
                    StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                _logger.Error($"[DeleteAllGroupMembersByGroupIdAsync] Exception: {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse("Internal Server Error", StatusCodes.Status500InternalServerError);
            }
        }


        public ApiResponseDto<object> InsertOrUpdateAnnouncement(CreateAnnouncementRequest request)
        {
            try
            {
                int categoryId = request.CategoryId ?? 0;

                if (!string.IsNullOrWhiteSpace(request.CategoryName) && categoryId == 0)
                {
                    var newCategory = new MstAnnouncementCategory // replace with your actual Category entity
                    {
                        AnnoucementCategoryName = request.CategoryName,
                        CreatedAt = DateTime.UtcNow,
                        Status = true
                    };

                    _context.MstAnnouncementCategories.Add(newCategory);
                    _context.SaveChanges();

                    // Set the new category ID to use for the announcement
                    categoryId = newCategory.AnnoucementCategoryId; // Adjust property name to your model's PK
                }
                // Mapping CreateAnnouncementRequest to MstAnnouncementDto
                var announcementDto = new MstAnnouncementDto
                {
                    Title = request.Title,
                    Description = request.Description,
                    Status = (int)request.Status,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = request.CreatedBy,
                    AnnouncementDocument = request.AnnouncementDocument,
                    ScheduleAt = request.ScheduleAt,
                    ExpiryAt = request.ExpiryAt,
                    CategoryId = categoryId,
                    FacilityId =  request.FacilityId
                };
                var announcement = _mapper.Map<MstAnnouncement>(announcementDto);

                // Check if the announcement is new or existing
                if (announcement.AnnouncementsId == 0)
                {
                    _context.MstAnnouncements.Add(announcement);
                }
                else
                {
                    _context.MstAnnouncements.Update(announcement);
                }

                // Save announcement to the database
                _context.SaveChanges();

                // Add receivers based on the 'SendTo' value
                var receivers = new List<MstAnnouncementReceiverDto>();
                int announcementId = announcement.AnnouncementsId;

                switch (request.SendTo)
                {
                    case SendToType.User:
                        if (request.UserIds?.Any() == true)
                        {
                            receivers.AddRange(request.UserIds.Select(userId => new MstAnnouncementReceiverDto
                            {
                                AnnouncementId = announcementId,
                                UserId = userId,
                                Delivered = false
                            }));
                        }
                        break;

                    case SendToType.Group:
                        if (request.GroupId?.Any() == true)
                        {
                            receivers.AddRange(request.GroupId.Select(groupId => new MstAnnouncementReceiverDto
                            {
                                AnnouncementId = announcementId,
                                GroupId = groupId,
                                Delivered = false
                            }));
                        }
                        break;

                    default:
                        throw new ArgumentException("Invalid receiver configuration");
                }
                var announcementReceivers = _mapper.Map<List<MstAnnouncementReceiver>>(receivers);

                // Add receivers to the database
                _context.MstAnnouncementReceivers.AddRange(announcementReceivers);
                _context.SaveChanges();

                // Return success response
                return ApiResponseDto<object>.SuccessResponse(AppMessages.AnnouncementSaved, StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                _logger.Error($"[InsertOrUpdateAnnouncement] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }
        public ApiResponseDto<object> UpdateAnnouncement(CreateAnnouncementRequest request)
        {
            try
            {
                var announcement = _context.MstAnnouncements
                    .Include(a => a.MstAnnouncementReceivers)
                    .FirstOrDefault(a => a.AnnouncementsId == request.AnnouncementsId);

                if (announcement == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.AnnouncementNotFound, StatusCodes.Status404NotFound);
                }

                // Update main announcement properties
                announcement.Title = request.Title;
                announcement.Description = request.Description;
                announcement.Status = (int)request.Status;
                announcement.ModifiedAt = DateTime.UtcNow;
                announcement.ScheduleAt = request.ScheduleAt;
                announcement.ExpiryAt = request.ExpiryAt;
                announcement.CategoryId = request.CategoryId;
                announcement.FacilityId = request.FacilityId;
                // ✅ Only update document if provided
                if (!string.IsNullOrEmpty(request.AnnouncementDocument))
                {
                    announcement.AnnouncementDocument = request.AnnouncementDocument;
                }
                // Remove old receivers
                _context.MstAnnouncementReceivers.RemoveRange(announcement.MstAnnouncementReceivers);

                // Add new receivers
                var newReceivers = new List<MstAnnouncementReceiver>();

                switch (request.SendTo)
                {
                    case SendToType.User:
                        if (request.UserIds?.Any() == true)
                        {
                            newReceivers.AddRange(request.UserIds.Select(userId => new MstAnnouncementReceiver
                            {
                                AnnouncementId = request.AnnouncementsId ?? 0,
                                UserId = userId,
                                Delivered = false
                            }));
                        }
                        break;

                    case SendToType.Group:
                        if (request.GroupId?.Any() == true)
                        {
                            newReceivers.AddRange(request.GroupId.Select(groupId => new MstAnnouncementReceiver
                            {
                                AnnouncementId = request.AnnouncementsId ?? 0,
                                GroupId = groupId,
                                Delivered = false
                            }));
                        }
                        break;

                    default:
                        return ApiResponseDto<object>.ErrorResponse("Invalid receiver type", StatusCodes.Status400BadRequest);
                }

                _context.MstAnnouncementReceivers.AddRange(newReceivers);

                // Save changes
                _context.SaveChanges();

                return ApiResponseDto<object>.SuccessResponse(AppMessages.AnnouncementUpdated , StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                _logger.Error($"[UpdateAnnouncement] Exception - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }

        public ApiResponseDto<object> GetAnnouncements()
        {
            try
            {
                var announcements = _context.MstAnnouncements.ToList();
                var announcementDtos = _mapper.Map<List<MstAnnouncementDto>>(announcements);
                return ApiResponseDto<object>.SuccessResponse(AppMessages.AnnouncementRetrieved, StatusCodes.Status200OK, announcementDtos);
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetAnnouncements] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }
        public ApiResponseDto<object> GetAnnouncementsByUserId(int userId)
        {
            try
            {
                var announcements = _context.MstAnnouncements.Where(u=>u.CreatedBy == userId).ToList();
                var announcementDtos = _mapper.Map<List<MstAnnouncementDto>>(announcements);
                return ApiResponseDto<object>.SuccessResponse(AppMessages.AnnouncementRetrieved, StatusCodes.Status200OK, announcementDtos);
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetAnnouncementsByUserId] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }

        public ApiResponseDto<object> GetUsersAnnouncementsByUserId(int userId)
        {
            try
            {
                var today = DateTime.UtcNow.Date;

                // Get all groups user is part of
                var userGroupIds = _context.MstGroupMembers
                    .Where(g => g.UserId == userId)
                    .Select(g => g.GroupId)
                    .ToList();

                // Fetch announcements where user is directly or via group a receiver
                var announcements = _context.MstAnnouncements
         .Where(a =>
             a.Status == 1 &&
             (!a.ScheduleAt.HasValue || a.ScheduleAt.Value.Date <= today) &&
             (!a.ExpiryAt.HasValue || a.ExpiryAt.Value.Date >= today) &&
             a.MstAnnouncementReceivers.Any(r =>
                 r.UserId == userId ||
                 (r.GroupId.HasValue && userGroupIds.Contains(r.GroupId.Value))
             )
         ).Select(a => new
                    {
                        a.AnnouncementsId,
                        a.Title,
                        a.Description,
                        a.ScheduleAt,
                        a.ExpiryAt,
                        a.CreatedAt,
                        DocumentPath = a.AnnouncementDocument,
                        PublishedBy = _context.MstUsers
                            .Where(u => u.UserId == a.CreatedBy)
                            .Select(u => u.FirstName ?? u.Email)
                            .FirstOrDefault()
                    })
                    .ToList();

                return ApiResponseDto<object>.SuccessResponse(
                    AppMessages.AnnouncementRetrieved,
                    StatusCodes.Status200OK,
                    announcements
                );
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetUsersAnnouncementsByUserId] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(
                    AppMessages.InternalServerError,
                    StatusCodes.Status500InternalServerError
                );
            }
        }

        //public async Task<ApiResponseDto<object>> GetAnnouncementByIdAsync(int announcementId)
        //{
        //    try
        //    {
        //        var announcement = await _context.MstAnnouncements.FindAsync(announcementId);
        //        if (announcement == null)
        //        {
        //            return ApiResponseDto<object>.ErrorResponse(AppMessages.AnnouncementNotFound, StatusCodes.Status404NotFound);
        //        }
        //        var announcementDto = _mapper.Map<MstAnnouncementDto>(announcement);
        //        return ApiResponseDto<object>.SuccessResponse(AppMessages.AnnouncementsRetrieved, StatusCodes.Status200OK, announcementDto);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.Error($"[GetAnnouncementById] Exception occurred - {ex.Message}");
        //        return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
        //    }
        //}
        public async Task<ApiResponseDto<object>> GetAnnouncementByIdAsync(int announcementId)
        {
            try
            {
                var announcement = await _context.MstAnnouncements
                    .Include(a => a.MstAnnouncementReceivers)
                        .ThenInclude(r => r.User)
                    .Include(a => a.MstAnnouncementReceivers)
                        .ThenInclude(r => r.Group)
                    .FirstOrDefaultAsync(a => a.AnnouncementsId == announcementId);

                if (announcement == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.AnnouncementNotFound, StatusCodes.Status404NotFound);
                }

                var groupName = announcement.MstAnnouncementReceivers
                                    .Where(r => r.Group != null)
                                    .Select(r => r.Group.GroupName)
                                    .FirstOrDefault();

                var userNames = announcement.MstAnnouncementReceivers
                                    .Where(r => r.User != null)
                                    .Select(r => r.User.Username)
                                    .ToList();

                var dto = new
                {
                    announcement.Title,
                    announcement.Description,
                    ScheduleAt = announcement.ScheduleAt?.ToString("yyyy-MM-dd"),
                    ExpiryAt = announcement.ExpiryAt?.ToString("yyyy-MM-dd"),
                    GroupName = groupName,
                    UserNames = userNames
                };

                return ApiResponseDto<object>.SuccessResponse(AppMessages.AnnouncementsRetrieved, StatusCodes.Status200OK, dto);
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetAnnouncementById] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<object>> GetAnnouncementDetailsByIdAsync(int announcementId)
        {
            try
            {
                var announcement = await _context.MstAnnouncements
                    .Include(a => a.MstAnnouncementReceivers)
                        .ThenInclude(r => r.User)
                    .Include(a => a.MstAnnouncementReceivers)
                        .ThenInclude(r => r.Group)
                             .Include(a => a.Category)
                    .FirstOrDefaultAsync(a => a.AnnouncementsId == announcementId);

                if (announcement == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.AnnouncementNotFound, StatusCodes.Status404NotFound);
                }

                // Check if this is a group announcement
                bool isGroupBased = announcement.MstAnnouncementReceivers.Any(r => r.GroupId != null);
                bool isUserBased = announcement.MstAnnouncementReceivers.Any(r => r.UserId != null);

                // Prepare response
                var announcementDto = new
                {
                    announcement.AnnouncementsId,
                    announcement.Title,
                    announcement.Description,
                    announcement.Status,
                    announcement.AnnouncementDocument,
                    announcement.CategoryId,
                    announcement.FacilityId,
                    PublishedBy = _context.MstUsers
                            .Where(u => u.UserId == announcement.CreatedBy)
                            .Select(u => u.FirstName ?? u.Email)
                            .FirstOrDefault(),
                    CategoryName = announcement?.Category?.AnnoucementCategoryName,
                    ScheduleAt = announcement.ScheduleAt?.ToString("yyyy-MM-ddTHH:mm"),
                    ExpiryAt = announcement.ExpiryAt?.ToString("yyyy-MM-ddTHH:mm"),
                    IsGroupBased = isGroupBased,
                    GroupId = isGroupBased ? announcement.MstAnnouncementReceivers.Where(r => r.GroupId != null).Select(r => r.GroupId.Value)
                                                .ToList() : new List<int>(),
                    GroupName = isGroupBased ? announcement.MstAnnouncementReceivers.FirstOrDefault(r => r.Group != null)?.Group?.GroupName : null,
                    UserIds = isUserBased ? announcement.MstAnnouncementReceivers
                                                .Where(r => r.UserId != null)
                                                .Select(r => r.UserId.Value)
                                                .ToList() : new List<int>(),
                    UserNames = isUserBased ? announcement.MstAnnouncementReceivers
                                                  .Where(r => r.User != null)
                                                  .Select(r => r.User.FirstName)
                                                  .ToList() : new List<string>()
                };

                return ApiResponseDto<object>.SuccessResponse(AppMessages.AnnouncementsRetrieved, StatusCodes.Status200OK, announcementDto);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Error retrieving announcement", StatusCodes.Status500InternalServerError, ex.Message);
            }
        }

        public async Task<ApiResponseDto<object>> DeleteAnnouncementAsync(int announcementId)
        {
            try
            {
                var announcement = await _context.MstAnnouncements
                    .Include(a => a.MstAnnouncementReceivers) // Include related receivers
                    .FirstOrDefaultAsync(a => a.AnnouncementsId == announcementId);

                if (announcement == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.AnnouncementNotFound, StatusCodes.Status404NotFound);
                }

                // Remove associated receivers
                if (announcement.MstAnnouncementReceivers != null && announcement.MstAnnouncementReceivers.Any())
                {
                    _context.MstAnnouncementReceivers.RemoveRange(announcement.MstAnnouncementReceivers);
                }

                // Remove the announcement itself
                _context.MstAnnouncements.Remove(announcement);
                await _context.SaveChangesAsync();

                return ApiResponseDto<object>.SuccessResponse(AppMessages.AnnouncementDeleted, StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                _logger.Error($"[DeleteAnnouncement] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }


        public async Task<ApiResponseDto<object>> InsertAnnouncementReceiverAsync(MstAnnouncementReceiverDto dto)
        {
            try
            {
                if (dto == null || dto.AnnouncementId <= 0)
                {
                    return ApiResponseDto<object>.ErrorResponse("Invalid input data.", StatusCodes.Status400BadRequest);
                }

                var receiver = new MstAnnouncementReceiver
                {
                    AnnouncementId = dto.AnnouncementId,
                    UserId = dto.UserId,
                    GroupId = dto.GroupId,
                    Delivered = false
                };

                _context.MstAnnouncementReceivers.Add(receiver);
                await _context.SaveChangesAsync();

                return ApiResponseDto<object>.SuccessResponse(AppMessages.AnnouncementSaved, StatusCodes.Status200OK, receiver);
            }
            catch (Exception ex)
            {
                _logger.Error($"[InsertAnnouncementReceiverAsync] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<IEnumerable<object>>> GetAnnouncementReceiversAsync(int? userId = null, int? groupId = null, bool? delivered = null)
        {
            try
            {
                var announcements = await _context.MstAnnouncementReceivers
                    .Where(receiver =>
                        (!userId.HasValue || receiver.UserId == userId) &&
                        (!groupId.HasValue || receiver.GroupId == groupId) &&
                        (!delivered.HasValue || receiver.Delivered == delivered))
                    .Select(receiver => new 
                    {
                        AnnouncementsId = receiver.Announcement.AnnouncementsId,
                        Title = receiver.Announcement.Title,
                        Description = receiver.Announcement.Description,
                        CreatedAt = receiver.Announcement.CreatedAt,
                        UserId = receiver.UserId,
                        GroupId = receiver.GroupId,
                        Delivered = receiver.Delivered
                    })
                    .ToListAsync();

                if (announcements == null || !announcements.Any())
                {
                    return ApiResponseDto<IEnumerable<object>>.SuccessResponse(AppMessages.AnnouncementNotFound, StatusCodes.Status200OK, new List<MstAnnouncementDto>());
                }

                return ApiResponseDto<IEnumerable<object>>.SuccessResponse(AppMessages.AnnouncementSaved, StatusCodes.Status200OK, announcements);
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetAnnouncementsAsync] Exception occurred - {ex.Message}");
                return ApiResponseDto<IEnumerable<object>>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<object>> ToggleAnnouncementAsync(int announcementId, int statusId)
        {
            try
            {
                var entity = await _context.MstAnnouncements.FindAsync(announcementId);
                if (entity == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.NewsletterNotFound, StatusCodes.Status404NotFound);
                }
                entity.Status = statusId;
                await _context.SaveChangesAsync();
                var message = "Status Updated Successfully";
                return ApiResponseDto<object>.SuccessResponse(message, StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> InsertOrUpdateAnnouncementCategoryAsync(MstAnnoucementCategoryDto dto)
        {
            try
            {
                MstAnnouncementCategory entity;
                bool isUpdate = dto.AnnoucementCategoryId > 0;
                if (isUpdate)
                {
                    entity = await _context.MstAnnouncementCategories.FindAsync(dto.AnnoucementCategoryId);
                    if (entity == null)
                    {
                        return ApiResponseDto<object>.ErrorResponse("Announcement category not found", StatusCodes.Status404NotFound);
                    }
                    _mapper.Map(dto, entity);
                    _context.MstAnnouncementCategories.Update(entity);
                }
                else
                {
                    entity = _mapper.Map<MstAnnouncementCategory>(dto);
                    _context.MstAnnouncementCategories.Add(entity);
                }
                await _context.SaveChangesAsync();
                return ApiResponseDto<object>.SuccessResponse(isUpdate ? "Announcement category updated successfully" : "Announcement category created successfully", StatusCodes.Status200OK, _mapper.Map<MstAnnoucementCategoryDto>(entity));
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> GetAnnouncementCategoriesByUserIdAsync(int userId)
        {
            try
            {
                var categories = await _context.MstAnnouncementCategories.Where(x => x.CreatedBy == userId).ToListAsync();
                var dtos = _mapper.Map<List<MstAnnoucementCategoryDto>>(categories);
                return ApiResponseDto<object>.SuccessResponse("Announcement categories retrieved", StatusCodes.Status200OK, dtos);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> GetAnnouncementCategoriesAsync()
        {
            try
            {
                var categories = await _context.MstAnnouncementCategories.Where(x=> x.Status).ToListAsync();
                var dtos = _mapper.Map<List<MstAnnoucementCategoryDto>>(categories);
                return ApiResponseDto<object>.SuccessResponse("Announcement categories retrieved", StatusCodes.Status200OK, dtos);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<object>> GetAnnouncementCategoryByIdAsync(int categoryId)
        {
            try
            {
                var category = await _context.MstAnnouncementCategories.FindAsync(categoryId);
                if (category == null)
                {
                    return ApiResponseDto<object>.ErrorResponse("Announcement category not found", StatusCodes.Status404NotFound);
                }
                var dto = _mapper.Map<MstAnnoucementCategoryDto>(category);
                return ApiResponseDto<object>.SuccessResponse("Announcement category retrieved", StatusCodes.Status200OK, dto);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> ToggleAnnouncementCategoryStatusAsync(int categoryId)
        {
            try
            {
                var entity = await _context.MstAnnouncementCategories.FindAsync(categoryId);
                if (entity == null)
                {
                    return ApiResponseDto<object>.ErrorResponse("Announcement category not found", StatusCodes.Status404NotFound);
                }
                entity.Status = !entity.Status;
                await _context.SaveChangesAsync();
                var message = entity.Status ? AppMessages.AnnouncementCategoryActivatedSuccessfully : AppMessages.AnnouncementCategoryDeactivatedSuccessfully;
                return ApiResponseDto<object>.SuccessResponse(message, StatusCodes.Status200OK, entity.Status);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }

    }
}
