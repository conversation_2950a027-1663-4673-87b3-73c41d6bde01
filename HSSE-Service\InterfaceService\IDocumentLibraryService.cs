using HSSE_ModelDto.ModelDto;
using System.Collections.Generic;

namespace HSSE_Service.InterfaceService
{
    public interface IDocumentLibraryService
    {
        ApiResponseDto<MstDocumentLibraryDto> InsertOrUpdateDocument(MstDocumentLibraryDto dto);
        ApiResponseDto<List<DocumentHierarchyDto>> GetDocuments();
        ApiResponseDto<List<DocumentHierarchyDto>> GetDocumentsByUserId(int userId);
        ApiResponseDto<MstDocumentLibraryDto> GetDocumentById(int documentId);
        ApiResponseDto<object> DeleteDocument(int documentId, int deletedBy);
    }
}