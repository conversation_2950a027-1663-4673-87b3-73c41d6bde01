﻿using HSSE_ModelDto.ModelDto;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Text.RegularExpressions;
using HSSE_API.Helper;

namespace HSSE_API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class DocumentLibraryController : ControllerBase
    {
        private readonly IDocumentLibraryService _service;

        public DocumentLibraryController(IDocumentLibraryService service)
        {
            _service = service;
        }

        [HttpPost]
        public IActionResult CreateOrUpdateDocument([FromBody] MstDocumentLibraryDto dto)
        {
            if (!string.IsNullOrEmpty(dto.DocumentUrl))
            {
                // Folder setup
                string externalRootPath = @"C:\HSSE-Announcements";
                string uploadFolder = Path.Combine(externalRootPath, "Doc-Library");

                if (!Directory.Exists(uploadFolder))
                {
                    Directory.CreateDirectory(uploadFolder);
                }

                // Base64 cleanup
                string base64Data = dto.DocumentUrl;
                string fileExtension = "pdf"; // default
                if (base64Data.Contains(","))
                {
                    var header = base64Data.Substring(0, base64Data.IndexOf(","));
                    base64Data = base64Data.Substring(base64Data.IndexOf(",") + 1);

                    // Try to extract the file type from base64 header (e.g., data:application/pdf;base64)
                    var match = Regex.Match(header, @"data:(.+?);base64");
                    if (match.Success)
                    {
                        var mimeType = match.Groups[1].Value;
                        fileExtension = MimeHelper.GetExtensionFromMimeType(mimeType);
                    }
                }

                // Generate unique file name
                string uniqueFileName = $"{Guid.NewGuid()}.{fileExtension}";
                string filePath = Path.Combine(uploadFolder, uniqueFileName);

                // Save file
                byte[] fileBytes = Convert.FromBase64String(base64Data);
                System.IO.File.WriteAllBytes(filePath, fileBytes);

                // Set public URL path
                dto.DocumentUrl = $"/ExternalFiles/Doc-Library/{uniqueFileName}";
            }
            var result = _service.InsertOrUpdateDocument(dto);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public IActionResult GetDocuments()
        {
            var result = _service.GetDocuments();
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public IActionResult GetDocumentsByUserId(int userId)
        {
            var result = _service.GetDocumentsByUserId(userId);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public IActionResult GetDocumentById(int documentId)
        {
            var result = _service.GetDocumentById(documentId);
            return StatusCode(result.Status, result);
        }
        [HttpDelete]
        public IActionResult DeleteDocument(int documentId, int deletedBy)
        {
            var result = _service.DeleteDocument(documentId, deletedBy);
            return StatusCode(result.Status, result);
        }
    }
} 