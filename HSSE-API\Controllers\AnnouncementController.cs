using HSSE_API.NLog;
using HSSE_ModelDto.ModelDto;
using HSSE_Service.Constants;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Mvc;
using System.Text.RegularExpressions;

namespace HSSE_API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class AnnouncementController : ControllerBase
    {

        private ILog _logger;
        private string? controllerName;
        private IConfiguration _configuration;
        private readonly IAnnouncementService _service;
        private string? actionName;
        public AnnouncementController(IConfiguration configuration, ILog logger, IAnnouncementService service)
        {
            _logger = logger;
            _service = service;
            _configuration = configuration;
        }
        [HttpPost]
        public IActionResult InsertOrUpdateGroup([FromBody] MstGroupDto dto)
        {
            _logger.Information($"[InsertOrUpdateGroup] Request: {dto}");

            try
            {
                var result = _service.InsertOrUpdateGroup(dto);
                _logger.Information($"[InsertOrUpdateGroup] Success: {result.Data}");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error( "[InsertOrUpdateGroup] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpGet]
        public IActionResult GetGroups()
        {
            _logger.Information("[GetGroups] Request received");

            try
            {
                var result = _service.GetGroups();
                _logger.Information("[GetGroups] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error( "[GetGroups] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpGet]
        public IActionResult GetGroupById(int id)
        {
            _logger.Information($"[GetGroupById] Request: GroupId={id}");

            try
            {
                var result = _service.GetGroupById(id);
                _logger.Information("[GetGroupById] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[GetGroupById] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteGroup(int id)
        {
            _logger.Information($"[DeleteGroup] Request: GroupId={id}");

            try
            {
                var result = await _service.DeleteGroupAsync(id);
                _logger.Information("[DeleteGroup] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error( "[DeleteGroup] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpPost]
        public async Task<IActionResult> InsertOrUpdateGroupMember([FromBody] MstGroupMemberDto dto)
        {
            _logger.Information($"[InsertOrUpdateGroupMember] Request: {dto}");

            try
            {
                var result = await _service.InsertOrUpdateGroupMemberAsync(dto);
                _logger.Information("[InsertOrUpdateGroupMember] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error( "[InsertOrUpdateGroupMember] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }
        [HttpPost]
        public async Task<IActionResult> InsertGroupMembers([FromBody] MstGroupMemberDto dto)
        {
            _logger.Information($"[InsertOrUpdateGroupMember] Request: {dto}");

            try
            {
                var result = await _service.InsertGroupMembersAsync(dto);
                _logger.Information("[InsertOrUpdateGroupMember] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[InsertOrUpdateGroupMember] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }
        [HttpPost]
        public async Task<IActionResult> UpdateGroupMembers([FromBody] MstGroupMemberDto dto)
        {
            _logger.Information($"[InsertOrUpdateGroupMember] Request: {dto}");

            try
            {
                var result = await _service.UpdateGroupMembersAsync(dto);
                _logger.Information("[InsertOrUpdateGroupMember] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[InsertOrUpdateGroupMember] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetGroupMembers()
        {
            _logger.Information("[GetGroupMembers] Request received");

            try
            {
                var result = await _service.GetGroupMembersAsync();
                _logger.Information("[GetGroupMembers] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error( "[GetGroupMembers] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetGroupMembersByGroupId(int groupId)
        {
            _logger.Information("[GetGroupMembers] Request received");

            try
            {
                var result = await _service.GetGroupMembersByGroupIdAsync(groupId);
                _logger.Information("[GetGroupMembers] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[GetGroupMembers] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }
        
        [HttpGet]
        public async Task<IActionResult> GetGroupMemberById(int id)
        {
            _logger.Information($"[GetGroupMemberById] Request: MemberId={id}");

            try
            {
                var result = await _service.GetGroupMemberByIdAsync(id);
                _logger.Information("[GetGroupMemberById] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error( "[GetGroupMemberById] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteAllGroupMembersByGroupId(int id)
        {
            _logger.Information($"[DeleteGroupMember] Request: MemberId={id}");

            try
            {
                var result = await _service.DeleteAllGroupMembersByGroupIdAsync(id);
                _logger.Information("[DeleteGroupMember] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error( "[DeleteGroupMember] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpPost]
        public async Task<IActionResult> InsertOrUpdateAnnouncement([FromBody] CreateAnnouncementRequest dto)
        {
            _logger.Information($"[InsertOrUpdateAnnouncement] Request: {dto}");

            try
            {
                if (!string.IsNullOrEmpty(dto.Base64File) && !string.IsNullOrEmpty(dto.FileName))
                {
                    // ✅ Save in external folder on C: drive
                    string externalRootPath = @"C:\HSSE-Announcements";
                    string uploadFolder = Path.Combine(externalRootPath, "profile-images");

                    if (!Directory.Exists(uploadFolder))
                    {
                        Directory.CreateDirectory(uploadFolder);
                    }

                    string uniqueFileName = $"{Guid.NewGuid()}_{dto.FileName}";
                    string filePath = Path.Combine(uploadFolder, uniqueFileName);

                    // Strip base64 prefix if exists
                    string base64Data = dto.Base64File;
                    if (base64Data.Contains(","))
                    {
                        base64Data = base64Data.Substring(base64Data.IndexOf(",") + 1);
                    }

                    byte[] fileBytes = Convert.FromBase64String(base64Data);
                    System.IO.File.WriteAllBytes(filePath, fileBytes);

                    // Save virtual download path
                    dto.AnnouncementDocument = $"/ExternalFiles/profile-images/{uniqueFileName}";
                }
                var result = _service.InsertOrUpdateAnnouncement(dto);
                _logger.Information("[InsertOrUpdateAnnouncement] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error( "[InsertOrUpdateAnnouncement] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }
        [HttpPost]
        public async Task<IActionResult> UpdateAnnouncement([FromBody] CreateAnnouncementRequest dto)
        {
            _logger.Information($"[UpdateAnnouncement] Request: {dto}");

            try
            {
                if (!string.IsNullOrEmpty(dto.Base64File) && !string.IsNullOrEmpty(dto.FileName))
                {
                    // ✅ Save in external folder on C: drive
                    string externalRootPath = @"C:\HSSE-Announcements";
                    string uploadFolder = Path.Combine(externalRootPath, "profile-images");

                    if (!Directory.Exists(uploadFolder))
                    {
                        Directory.CreateDirectory(uploadFolder);
                    }

                    string uniqueFileName = $"{Guid.NewGuid()}_{dto.FileName}";
                    string filePath = Path.Combine(uploadFolder, uniqueFileName);

                    // Strip base64 prefix if exists
                    string base64Data = dto.Base64File;
                    if (base64Data.Contains(","))
                    {
                        base64Data = base64Data.Substring(base64Data.IndexOf(",") + 1);
                    }

                    byte[] fileBytes = Convert.FromBase64String(base64Data);
                    await System.IO.File.WriteAllBytesAsync(filePath, fileBytes);

                    // Save virtual download path
                    dto.AnnouncementDocument = $"/ExternalFiles/profile-images/{uniqueFileName}";
                }

                var result = await _service.UpdateAnnouncementAsync(dto);
                _logger.Information("[UpdateAnnouncement] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error($"[UpdateAnnouncement] Exception: {ex.Message}");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetAnnouncements()
        {
            _logger.Information("[GetAnnouncements] Request received");

            try
            {
                var result = await _service.GetAnnouncementsAsync();
                _logger.Information("[GetAnnouncements] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error( "[GetAnnouncements] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetAnnouncementsByUserId(int userId)
        {
            _logger.Information("[GetAnnouncements] Request received");

            try
            {
                var result = await _service.GetAnnouncementsByUserIdAsync(userId);
                _logger.Information("[GetAnnouncements] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[GetAnnouncements] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetUsersAnnouncementsByUserId(int userId)
        {
            _logger.Information("[GetAnnouncements] Request received");

            try
            {
                var result = await _service.GetUsersAnnouncementsByUserIdAsync(userId);
                _logger.Information("[GetAnnouncements] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[GetAnnouncements] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetAnnouncementDetailsById(int id)
        {
            _logger.Information("[GetAnnouncements] Request received");

            try
            {
                var result = await _service.GetAnnouncementDetailsByIdAsync(id);
                _logger.Information("[GetAnnouncements] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[GetAnnouncements] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetAnnouncementById(int id)
        {
            _logger.Information($"[GetAnnouncementById] Request: AnnouncementId={id}");

            try
            {
                var result = await _service.GetAnnouncementByIdAsync(id);
                _logger.Information("[GetAnnouncementById] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error( "[GetAnnouncementById] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteAnnouncement(int id)
        {
            _logger.Information($"[DeleteAnnouncement] Request: AnnouncementId={id}");

            try
            {
                var result = await _service.DeleteAnnouncementAsync(id);
                _logger.Information("[DeleteAnnouncement] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error( "[DeleteAnnouncement] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpPost]
        public async Task<IActionResult> InsertAnnouncementReceiver([FromBody] MstAnnouncementReceiverDto dto)
        {
            _logger.Information($"[InsertAnnouncementReceiver] Request: {dto}");

            try
            {
                var result = await _service.InsertAnnouncementReceiverAsync(dto);
                _logger.Information("[InsertAnnouncementReceiver] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error( "[InsertAnnouncementReceiver] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetAnnouncementReceivers()
        {
            _logger.Information("[GetAnnouncementReceivers] Request received");

            try
            {
                var result = await _service.GetAnnouncementReceiversAsync();
                _logger.Information("[GetAnnouncementReceivers] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error( "[GetAnnouncementReceivers] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }
        [HttpPost]
        public async Task<IActionResult> ToggleAnnouncementStatus(int announcementId, int statusId)
        {
            var result = await _service.ToggleAnnouncementAsync(announcementId, statusId);
            return StatusCode(result.Status, result);
        }

        [HttpPost]
        public async Task<IActionResult> InsertOrUpdateAnnouncementCategory([FromBody] MstAnnoucementCategoryDto dto)
        {
            _logger.Information($"[InsertOrUpdateAnnouncementCategory] Request: {dto}");
            try
            {
                var result = await _service.InsertOrUpdateAnnouncementCategoryAsync(dto);
                _logger.Information($"[InsertOrUpdateAnnouncementCategory] Success: {result.Data}");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[InsertOrUpdateAnnouncementCategory] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetAnnouncementCategoriesByUserId(int userId)
        {
            _logger.Information($"[GetAnnouncementCategoriesByUserId] Request: userId={userId}");
            try
            {
                var result = await _service.GetAnnouncementCategoriesByUserIdAsync(userId);
                _logger.Information("[GetAnnouncementCategoriesByUserId] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[GetAnnouncementCategoriesByUserId] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetAnnouncementCategories()
        {
            _logger.Information($"[GetAnnouncementCategories]");
            try
            {
                var result = await _service.GetAnnouncementCategoriesAsync();
                _logger.Information("[GetAnnouncementCategories] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[GetAnnouncementCategories] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }
        
        [HttpGet]
        public async Task<IActionResult> GetAnnouncementCategoryById(int id)
        {
            _logger.Information($"[GetAnnouncementCategoryById] Request: id={id}");
            try
            {
                var result = await _service.GetAnnouncementCategoryByIdAsync(id);
                _logger.Information("[GetAnnouncementCategoryById] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[GetAnnouncementCategoryById] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpPost]
        public async Task<IActionResult> ToggleAnnouncementCategoryStatus(int id)
        {
            _logger.Information($"[ToggleAnnouncementCategoryStatus] Request: id={id}");
            try
            {
                var result = await _service.ToggleAnnouncementCategoryStatusAsync(id);
                _logger.Information("[ToggleAnnouncementCategoryStatus] Success");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error("[ToggleAnnouncementCategoryStatus] Exception");
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }
    }
}
