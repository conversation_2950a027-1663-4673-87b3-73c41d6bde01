using HSSE_ModelDto.ModelDto;
using HSSE_Service.Constants;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Mvc;
using System;

namespace HSSE_API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class InspectionController : ControllerBase
    {
        private readonly IInspectionService _service;

        public InspectionController(IInspectionService service)
        {
            _service = service;
        }

        [HttpPost]
        public IActionResult CreateOrUpdateActionParty([FromBody] MstActionPartyDto dto)
        {
            var result = _service.CreateOrUpdateActionParty(dto);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public IActionResult GetActionPartyById(int id)
        {
            var result = _service.GetActionPartyById(id);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public IActionResult GetAllActionPartyByFacilityId(int facilityId)
        {
            var result = _service.GetActionPartiesByFacilityId(facilityId);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public IActionResult GetActionPartyByUserId(int userId)
        {
            var result = _service.GetActionPartiesByUserId(userId);
            return StatusCode(result.Status, result);
        }
        [HttpPost]
        public IActionResult ToggleActionPartiesActivation(int actionPartyId)
        {
            try
            {
                var result = _service.ToggleActionPartiesActivation(actionPartyId);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, 500));
            }
        }

        [HttpPost]
        public IActionResult CreateOrUpdateInspection([FromBody] MstInspectionDto dto)
        {
            string externalRootPath = @"C:\HSSE-Announcements";
            string uploadFolder = Path.Combine(externalRootPath, "inspection-files");

            if (!Directory.Exists(uploadFolder))
                Directory.CreateDirectory(uploadFolder);

            // Save Observation Attachment
            if (!string.IsNullOrEmpty(dto.ObservationAttachmentBase64) && !string.IsNullOrEmpty(dto.ObservationAttachmentName))
            {
                string uniqueFileName = $"{Guid.NewGuid()}_{dto.ObservationAttachmentName}";
                string filePath = Path.Combine(uploadFolder, uniqueFileName);

                string base64Data = dto.ObservationAttachmentBase64.Contains(",")
                    ? dto.ObservationAttachmentBase64.Substring(dto.ObservationAttachmentBase64.IndexOf(",") + 1)
                    : dto.ObservationAttachmentBase64;

                byte[] fileBytes = Convert.FromBase64String(base64Data);
                System.IO.File.WriteAllBytes(filePath, fileBytes);

                dto.ObservationAttachmentBase64 = $"/ExternalFiles/inspection-files/{uniqueFileName}";
            }

            // Save Recommendation Attachment
            if (!string.IsNullOrEmpty(dto.RecommendationAttachmentBase64) && !string.IsNullOrEmpty(dto.RecommendationAttachmentName))
            {
                string uniqueFileName = $"{Guid.NewGuid()}_{dto.RecommendationAttachmentName}";
                string filePath = Path.Combine(uploadFolder, uniqueFileName);

                string base64Data = dto.RecommendationAttachmentBase64.Contains(",")
                    ? dto.RecommendationAttachmentBase64.Substring(dto.RecommendationAttachmentBase64.IndexOf(",") + 1)
                    : dto.RecommendationAttachmentBase64;

                byte[] fileBytes = Convert.FromBase64String(base64Data);
                System.IO.File.WriteAllBytes(filePath, fileBytes);

                dto.RecommendationAttachmentBase64 = $"/ExternalFiles/inspection-files/{uniqueFileName}";
            }
            var result = _service.CreateOrUpdateInspection(dto);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public IActionResult GetInspectionById(int inspectionId)
        {
            var result = _service.GetInspectionById(inspectionId);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public IActionResult GetInspectionsByActionParty(int actionPartyName, int? status)
        {
            var result = _service.GetInspectionsByActionParty(actionPartyName, status);
            return StatusCode(result.Status, result);
        }

        [HttpPost]
        public IActionResult UpdateInspectionItemStatus(UpdateInspectionStatusDto req)
        {
            try
            {

                string externalRootPath = @"C:\HSSE-Announcements";
                string uploadFolder = Path.Combine(externalRootPath, "inspection-files");

                if (!string.IsNullOrEmpty(req.AfterImageUrl) && !string.IsNullOrEmpty(req.AfterImageName))
                {
                    string uniqueFileName = $"{Guid.NewGuid()}_{req.AfterImageName}";
                    string filePath = Path.Combine(uploadFolder, uniqueFileName);

                    string base64Data = req.AfterImageUrl.Contains(",")
                        ? req.AfterImageUrl.Substring(req.AfterImageUrl.IndexOf(",") + 1)
                        : req.AfterImageUrl;

                    byte[] fileBytes = Convert.FromBase64String(base64Data);
                    System.IO.File.WriteAllBytes(filePath, fileBytes);

                    req.AfterImageUrl = $"/ExternalFiles/inspection-files/{uniqueFileName}";
                }
                var result = _service.UpdateInspectionItemStatus(req);
                return StatusCode(result.Status, result);
            }
            catch (Exception)
            {

                throw;
            }
        }

        [HttpGet]
        public IActionResult GetInspectionCategory()
        {
            var result = _service.GetInspectionCategory();
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public IActionResult GetUserInspections(int userId)
        {
            var result = _service.GetUserInspections(userId);
            return StatusCode(result.Status, result);
        }
    }
} 