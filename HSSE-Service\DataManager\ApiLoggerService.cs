﻿using HSSE_Domain.Models;
using HSSE_ModelDto.ModelDto;
using HSSE_Service.InterfaceService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace HSSE_Service.DataManager
{
    public class ApiLoggerService: IApiLoggerService
    {
        private readonly HsseDbLatestContext _context;

        public ApiLoggerService(HsseDbLatestContext context)
        {
            _context = context;
        }

        public void LogInfo(LogsDto log)
        {
            SaveLog(log, "Info");
        }

        public void LogError(LogsDto log)
        {
            SaveLog(log, "Error");
        }

        private void SaveLog(LogsDto log, string level)
        {
            try
            {
                var entity = new MstApiLog
                {
                    CreatedOn = log.CreatedOn,
                    Level = level,
                    Message = log.Message,
                    StackTrace = log.StackTrace,
                    Exception = log.Exception,
                    Logger = log.Logger,
                    Url = log.Url
                };

                _context.MstApiLogs.Add(entity);
                _context.SaveChanges();
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }
    }
}
