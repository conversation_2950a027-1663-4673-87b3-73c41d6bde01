﻿using AutoMapper;
using HSSE_Service.InterfaceService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HSSE_Domain.Models;
using HSSE_Service.NLog;
using HSSE_ModelDto.ModelDto;
using Microsoft.AspNetCore.Http;
using HSSE_Service.Constants;
using Microsoft.EntityFrameworkCore;
using System.Dynamic;
using System.Reflection;
using Microsoft.Extensions.Logging;
using System.Xml.Linq;
using HSSE_ModelDto.Enum;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
namespace HSSE_Service.DataManager
{
    public class PostsService : IPostsService
    {
        private IMapper _mapper;
        readonly HsseDbLatestContext _dbContext;
        private ILoggerManager _logger;
        private readonly IApiLoggerService _loggerService;

        public PostsService(IApiLoggerService loggerService, HsseDbLatestContext dbContext, IMapper mapper, ILoggerManager logger)
        {
            _dbContext = dbContext;
            _mapper = mapper;
            _logger = logger;
            _loggerService = loggerService;
        }
        public ApiResponseDto<MstPostDto> InsertOrUpdatePost(MstPostDto postDto)
        {
            var logTime = DateTime.UtcNow;
            var loggerName = nameof(InsertOrUpdatePost);

            try
            {
                _loggerService.LogInfo(new LogsDto
                {
                    CreatedOn = logTime,
                    Level = LogLevel.Information.ToString(),
                    Message = $"InsertOrUpdatePost started. PostId: {postDto.PostId}",
                    Url = "Service:InsertOrUpdatePost",
                    StackTrace = Environment.StackTrace,
                    Logger = loggerName
                });

                MstPost postEntity;

                if (postDto.PostId > 0)
                {
                    postEntity = _dbContext.MstPosts.Find(postDto.PostId);
                    if (postEntity == null)
                    {
                        _loggerService.LogError(new LogsDto
                        {
                            CreatedOn = DateTime.UtcNow,
                            Level = LogLevel.Warning.ToString(),
                            Message = $"Post not found with PostId: {postDto.PostId}",
                            Url = "Service:InsertOrUpdatePost",
                            Logger = loggerName
                        });

                        return ApiResponseDto<MstPostDto>.ErrorResponse(
                            AppMessages.PostNotFound,
                            StatusCodes.Status404NotFound
                        );
                    }

                    _mapper.Map(postDto, postEntity);
                    postEntity.UpdatedAt = DateTime.UtcNow;

                    _dbContext.MstPosts.Update(postEntity);
                    // Handle post media update
                    if (!string.IsNullOrEmpty(postDto.ImageBase64))
                    {
                        var postMediaEntity = _dbContext.MstPostMedia
                            .FirstOrDefault(x => x.PostId == postDto.PostId);

                        if (postMediaEntity != null)
                        {
                            postMediaEntity.MediaUrl = postDto.ImageBase64;
                            _dbContext.MstPostMedia.Update(postMediaEntity);
                        }
                        else
                        {
                            var media = new MstPostMedium
                            {
                                PostId = postEntity.PostId,
                                MediaUrl = postDto.ImageBase64,
                                CreatedAt = DateTime.UtcNow
                            };
                            _dbContext.MstPostMedia.Add(media);
                        }
                    }

                    _dbContext.SaveChanges();
                    _loggerService.LogInfo(new LogsDto
                    {
                        CreatedOn = DateTime.UtcNow,
                        Level = LogLevel.Information.ToString(),
                        Message = $"Post updated successfully. PostId: {postEntity.PostId}",
                        Url = "Service:InsertOrUpdatePost",
                        Logger = loggerName
                    });

                    return ApiResponseDto<MstPostDto>.SuccessResponse(
                        AppMessages.PostUpdatedSuccessfully,
                        StatusCodes.Status200OK,
                        _mapper.Map<MstPostDto>(postEntity)
                    );
                }
                else
                {
                    postEntity = _mapper.Map<MstPost>(postDto);
                    postEntity.CreatedAt = DateTime.UtcNow;

                    _dbContext.MstPosts.Add(postEntity);
                    _dbContext.SaveChanges();
                    if (postDto.ImageBase64 != null)
                    {
                        var media = new MstPostMedium
                        {
                            PostId = postEntity.PostId,
                            MediaUrl = postDto.ImageBase64,
                            CreatedAt = DateTime.UtcNow
                        };
                        _dbContext.MstPostMedia.Add(media);
                    }

                    _loggerService.LogInfo(new LogsDto
                    {
                        CreatedOn = DateTime.UtcNow,
                        Level = LogLevel.Information.ToString(),
                        Message = $"Post created successfully. PostId: {postEntity.PostId}",
                        Url = "Service:InsertOrUpdatePost",
                        Logger = loggerName
                    });

                    return ApiResponseDto<MstPostDto>.SuccessResponse(
                        AppMessages.PostCreatedSuccessfully,
                        StatusCodes.Status200OK,
                        null
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"[{loggerName}] Exception - {ex.Message}");

                _loggerService.LogError(new LogsDto
                {
                    CreatedOn = DateTime.UtcNow,
                    Level = LogLevel.Error.ToString(),
                    Message = $"Exception in {loggerName}: {ex.Message}",
                    Url = "Service:InsertOrUpdatePost",
                    Exception = ex.ToString(),
                    StackTrace = ex.StackTrace,
                    Logger = loggerName
                });

                var errors = new List<Error>
        {
            new Error
            {
                Message = ex.Message,
                Code = StatusCodes.Status500InternalServerError
            }
        };

                return ApiResponseDto<MstPostDto>.ErrorResponse(
                    AppMessages.InternalServerError,
                    StatusCodes.Status400BadRequest,
                    errors
                );
            }
        }
        public ApiResponseDto<dynamic> InsertLikestoPost(MstLikesConfigDto postDto)
        {
            var logTime = DateTime.UtcNow;
            var loggerName = nameof(InsertLikestoPost);

            try
            {
                _loggerService.LogInfo(new LogsDto
                {
                    CreatedOn = logTime,
                    Level = LogLevel.Information.ToString(),
                    Message = $"InsertLikestoPost started. PostId: {postDto.PostId}, UserId: {postDto.UserId}",
                    Url = "Service:InsertLikestoPost",
                    StackTrace = Environment.StackTrace,
                    Logger = loggerName
                });

                // Check if like already exists for the user and post
                var existingLike = _dbContext.MstLikesConfigs
                    .FirstOrDefault(x => x.PostId == postDto.PostId && x.UserId == postDto.UserId);

                if (existingLike != null)
                {
                    // Log that a like entry already exists
                    _logger.Information($"Like entry found for PostId: {postDto.PostId}, UserId: {postDto.UserId}. Toggling like status.");

                    // Toggle the IsLiked flag directly
                    existingLike.IsLiked = !existingLike.IsLiked;
                    existingLike.LikedAt = DateTime.UtcNow;

                    _dbContext.MstLikesConfigs.Update(existingLike);

                    if (existingLike.IsLiked)
                    {
                        _logger.Information($"PostId: {postDto.PostId} liked again by UserId: {postDto.UserId}.");
                    }
                    else
                    {
                        _logger.Warning($"PostId: {postDto.PostId} unliked by UserId: {postDto.UserId}.");
                    }
                }
                else
                {
                    // Log that no existing record was found, and adding a new like
                    _logger.Information($"No existing like found. Adding a new like for PostId: {postDto.PostId}, UserId: {postDto.UserId}.");

                    // No existing record, add new like
                    var postEntity = _mapper.Map<MstLikesConfig>(postDto);
                    postEntity.LikedAt = DateTime.UtcNow;
                    postEntity.IsLiked = true;

                    _dbContext.MstLikesConfigs.Add(postEntity);
                }

                _dbContext.SaveChanges();

                _loggerService.LogInfo(new LogsDto
                {
                    CreatedOn = DateTime.UtcNow,
                    Level = LogLevel.Information.ToString(),
                    Message = $"Like toggled for PostId: {postDto.PostId}, UserId: {postDto.UserId}",
                    Url = "Service:InsertLikestoPost",
                    Logger = loggerName
                });

                // Return only the IsLiked status in the response
                return ApiResponseDto<dynamic>.SuccessResponse(
                    AppMessages.PostLikedSuccessfully,
                    StatusCodes.Status200OK,
                    new { IsLiked = existingLike?.IsLiked ?? true } // Returns true if it's a new like
                );
            }
            catch (Exception ex)
            {
                _logger.Error($"[{loggerName}] Exception - {ex.Message}");

                _loggerService.LogError(new LogsDto
                {
                    CreatedOn = DateTime.UtcNow,
                    Level = LogLevel.Error.ToString(),
                    Message = $"Exception in {loggerName}: {ex.Message}",
                    Url = "Service:InsertLikestoPost",
                    Exception = ex.ToString(),
                    StackTrace = ex.StackTrace,
                    Logger = loggerName
                });

                var errors = new List<Error>
        {
            new Error
            {
                Message = ex.Message,
                Code = StatusCodes.Status500InternalServerError
            }
        };

                return ApiResponseDto<dynamic>.ErrorResponse(
                    AppMessages.InternalServerError,
                    StatusCodes.Status400BadRequest,
                    errors
                );
            }
        }

        public async Task<ApiResponseDto<List<MstPostDto>>> GetPostsAsync(int? userId, bool? showOnlyMine)
        {
            var logTime = DateTime.UtcNow;
            var loggerName = nameof(GetPostsAsync);

            try
            {
                await _loggerService.LogInfo(new LogsDto
                {
                    CreatedOn = logTime,
                    Level = LogLevel.Information.ToString(),
                    Message = $"Fetching posts. UserId: {(userId.HasValue ? userId.ToString() : "All")}",
                    Url = "Service:GetPostsAsync",
                    StackTrace = Environment.StackTrace,
                    Logger = loggerName
                });

                var query = _dbContext.MstPosts
      .Include(x => x.MstLikesConfigs)
      .Include(x => x.MstPostComments)
      .Include(x => x.MstPostMedia)
      .Include(x => x.Facility)
      .Where(p => !p.IsDeleted) // ✅ Exclude soft-deleted posts
      .AsQueryable();

                // Filter by facilities where the user is an admin
                if (showOnlyMine.HasValue && showOnlyMine.Value)
                {
                    query = query.Where(p => p.UserId == userId.Value); // Only user’s own posts
                }
                else
                {
                    // Get facility IDs where user is admin
                    var facilityIds = await _dbContext.MstUserRolesConfigs
                        .Where(urc => urc.UserId == userId.Value && urc.RoleId == 1)
                        .Select(urc => urc.FacilityId)
                        .Distinct()
                        .ToListAsync();

                    query = query.Where(p => facilityIds.Contains(p.FacilityId));
                }


                var posts = await query
                    .OrderByDescending(p => p.CreatedAt) // or CreatedOn, based on your actual field
                    .ToListAsync();
                if (!posts.Any())
                {
                    await _loggerService.LogInfo(new LogsDto
                    {
                        CreatedOn = DateTime.UtcNow,
                        Level = LogLevel.Warning.ToString(),
                        Message = "No posts found.",
                        Url = "Service:GetPostsAsync",
                        Logger = loggerName
                    });

                    return ApiResponseDto<List<MstPostDto>>.ErrorResponse(
                        AppMessages.PostNotFound,
                        StatusCodes.Status404NotFound
                    );
                }

                var postDtos = _mapper.Map<List<MstPostDto>>(posts);

                for (int i = 0; i < postDtos.Count; i++)
                {
                    var post = posts[i];
                    var dto = postDtos[i];

                    dto.LikeCount = post.MstLikesConfigs.Count(l => l.IsLiked);
                    dto.PostCommentsCount = post.MstPostComments.Count();
                    dto.ImageBase64 = post.MstPostMedia.FirstOrDefault()?.MediaUrl;
                    dto.AfterMediaUrls = post.MstPostMedia
     .Where(m => !string.IsNullOrWhiteSpace(m.AfterMediaUrl))
     .SelectMany(m => m.AfterMediaUrl.Split(',', StringSplitOptions.RemoveEmptyEntries))
     .Select(url => url.Trim())
     .ToList();
                    dto.IsLikedByUser = userId.HasValue && userId.Value > 0
                        ? post.MstLikesConfigs.Any(l => l.IsLiked && l.UserId == userId.Value)
                        : false;

                    dto.IsAuthor = userId.HasValue && userId.Value > 0 && post.UserId == userId.Value;

                    // Facility name
                    dto.FacilityName = post.Facility?.FacilityName;

                    // Assigned info
                    var assignment = await _dbContext.MstFollowupPosts
                        .FirstOrDefaultAsync(f => f.PostId == post.PostId && f.AssignedTo != null);

                    dto.IsAssigned = assignment != null;

                    if (assignment != null)
                    {
                        var assignedUserIds = assignment.AssignedTo?
                 .Split(',', StringSplitOptions.RemoveEmptyEntries)
                 .Select(id => int.TryParse(id, out var parsedId) ? parsedId : (int?)null)
                 .Where(id => id.HasValue)
                 .Select(id => id.Value)
                 .ToList() ?? new List<int>();

                        // ✅ Get usernames of assigned users
                        dto.AssignedToUsername = await _dbContext.MstUsers
                            .Where(u => assignedUserIds.Contains(u.UserId))
                            .Select(u => u.Username)
                            .ToListAsync();

                        // Assigned By
                        dto.AssignedByUsername = await _dbContext.MstUsers
                            .Where(u => u.UserId == assignment.CreatedBy)
                            .Select(u => u.Username)
                            .FirstOrDefaultAsync();

                        dto.CompletedBy = await _dbContext.MstUsers
                        .Where(u => u.UserId == assignment.CompletedBy)
                        .Select(u => u.Username)
                         .FirstOrDefaultAsync();

                        dto.CompletedComments = assignment.Comments;
                    }
                    else
                    {
                        dto.AssignedToUsername = null;
                        dto.AssignedByUsername = null;
                    }
                }

                await _loggerService.LogInfo(new LogsDto
                {
                    CreatedOn = DateTime.UtcNow,
                    Level = LogLevel.Information.ToString(),
                    Message = $"Posts fetched successfully. Count: {postDtos.Count}",
                    Url = "Service:GetPostsAsync",
                    Logger = loggerName
                });

                return ApiResponseDto<List<MstPostDto>>.SuccessResponse(
                    AppMessages.PostFetchSuccess,
                    StatusCodes.Status200OK,
                    postDtos
                );
            }
            catch (Exception ex)
            {
                _logger.Error($"[{loggerName}] Exception - {ex.Message}");

                await _loggerService.LogError(new LogsDto
                {
                    CreatedOn = DateTime.UtcNow,
                    Level = LogLevel.Error.ToString(),
                    Message = $"Exception in {loggerName}: {ex.Message}",
                    Url = "Service:GetPostsAsync",
                    Exception = ex.ToString(),
                    StackTrace = ex.StackTrace,
                    Logger = loggerName
                });

                return ApiResponseDto<List<MstPostDto>>.ErrorResponse(
                    AppMessages.InternalServerError,
                    StatusCodes.Status500InternalServerError
                );
            }
        }

        public async Task<ApiResponseDto<object>> InsertOrUpdatePostCategoryAsync(MstPostCategoryDto dto)
        {
            try
            {
                MstPostCategory entity;
                bool isUpdate = dto.CatId > 0;
                if (isUpdate)
                {
                    entity = await _dbContext.MstPostCategories.FindAsync(dto.CatId);
                    if (entity == null)
                    {
                        return ApiResponseDto<object>.ErrorResponse("Post category not found", StatusCodes.Status404NotFound);
                    }
                    _mapper.Map(dto, entity);
                    _dbContext.MstPostCategories.Update(entity);
                }
                else
                {
                    entity = _mapper.Map<MstPostCategory>(dto);
                    _dbContext.MstPostCategories.Add(entity);
                }
                await _dbContext.SaveChangesAsync();
                return ApiResponseDto<object>.SuccessResponse(isUpdate ? "Post category updated successfully" : "Post category created successfully", StatusCodes.Status200OK, _mapper.Map<MstPostCategoryDto>(entity));
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> GetPostCategoriesAsync()
        {
            try
            {
                var categories = await _dbContext.MstPostCategories.ToListAsync();
                var dtos = _mapper.Map<List<MstPostCategoryDto>>(categories);
                return ApiResponseDto<object>.SuccessResponse("Post categories retrieved", StatusCodes.Status200OK, dtos);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> GetPostCategoryByIdAsync(int catId)
        {
            try
            {
                var category = await _dbContext.MstPostCategories.FindAsync(catId);
                if (category == null)
                {
                    return ApiResponseDto<object>.ErrorResponse("Post category not found", StatusCodes.Status404NotFound);
                }
                var dto = _mapper.Map<MstPostCategoryDto>(category);
                return ApiResponseDto<object>.SuccessResponse("Post category retrieved", StatusCodes.Status200OK, dto);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> DeletePostCategoryAsync(int catId)
        {
            try
            {
                var entity = await _dbContext.MstPostCategories.FindAsync(catId);
                if (entity == null)
                {
                    return ApiResponseDto<object>.ErrorResponse("Post category not found", StatusCodes.Status404NotFound);
                }
                _dbContext.MstPostCategories.Remove(entity);
                await _dbContext.SaveChangesAsync();
                return ApiResponseDto<object>.SuccessResponse("Post category deleted successfully", StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<MstPostDto>> GetPostDetailsByIdAsync(int postId)
        {
            try
            {
                var post = await _dbContext.MstPosts
                    .Include(p => p.MstPostMedia)
                    .FirstOrDefaultAsync(p => p.PostId == postId);
                if (post == null)
                {
                    return ApiResponseDto<MstPostDto>.ErrorResponse("Post not found", StatusCodes.Status404NotFound);
                }
                var dto = _mapper.Map<MstPostDto>(post);
                // If there is media, set the first media's URL as ImageBase64 (or extend as needed)
                var media = post.MstPostMedia?.FirstOrDefault();
                if (media != null)
                {
                    dto.ImageBase64 = media.MediaUrl;
                }
                dto.LikeCount = post.MstLikesConfigs?.Count(l => l.IsLiked) ?? 0;
                return ApiResponseDto<MstPostDto>.SuccessResponse("Post details retrieved", StatusCodes.Status200OK, dto);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<MstPostDto>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<List<MstPostDto>>> GetPostDetailsAsync()
        {
            try
            {
                var post = await _dbContext.MstPosts.ToListAsync();
                if (post == null)
                {
                    return ApiResponseDto<List<MstPostDto>>.ErrorResponse("Post not found", StatusCodes.Status404NotFound);
                }
                var dto = _mapper.Map<List<MstPostDto>>(post);
                return ApiResponseDto<List<MstPostDto>>.SuccessResponse("Post details retrieved", StatusCodes.Status200OK, dto);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstPostDto>>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> InsertOrUpdatePostCommentAsync(MstPostCommentDto dto)
        {
            try
            {
                MstPostComment entity;
                bool isUpdate = dto.CommentId > 0;
                if (isUpdate)
                {
                    entity = await _dbContext.MstPostComments.FindAsync(dto.CommentId);
                    if (entity == null)
                    {
                        return ApiResponseDto<object>.ErrorResponse("Post category not found", StatusCodes.Status404NotFound);
                    }
                    _mapper.Map(dto, entity);
                    _dbContext.MstPostComments.Update(entity);
                }
                else
                {
                    dto.CommentedAt = DateTime.Now;
                    entity = _mapper.Map<MstPostComment>(dto);
                    _dbContext.MstPostComments.Add(entity);
                }
                await _dbContext.SaveChangesAsync();
                return ApiResponseDto<object>.SuccessResponse(isUpdate ? "Post category updated successfully" : "Post category created successfully", StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<List<MstPostCommentDto>>> GetPostCommentAsync(int postId)
        {
            try
            {
                var comments = await _dbContext.MstPostComments.Include(x => x.User).Where(c => c.PostId == postId).ToListAsync();
                if (comments == null)
                {
                    return ApiResponseDto<List<MstPostCommentDto>>.ErrorResponse("Post comments not found", StatusCodes.Status404NotFound);
                }
                var dto = comments.Select(c => new MstPostCommentDto
                {
                    CommentId = c.CommentId,
                    CommentText = c.CommentText,
                    UserName = c.User?.Username,
                    CommentedAt = c.CommentedAt
                }).ToList();
                return ApiResponseDto<List<MstPostCommentDto>>.SuccessResponse("Post comments retrieved", StatusCodes.Status200OK, dto);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstPostCommentDto>>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> InsertOrUpdateFollowupPostAsync(MstFollowupPostDto dto)
        {
            try
            {
                // Convert AssignedTo list to comma-separated string
                string? assignedToIds = dto.AssignedTo != null ? string.Join(",", dto.AssignedTo) : null;

                var existingEntity = await _dbContext.MstFollowupPosts
                    .FirstOrDefaultAsync(x => x.PostId == dto.PostId);

                if (existingEntity != null)
                {
                    // Update existing record
                    existingEntity.AssignedTo = assignedToIds;
                    existingEntity.AssignedTime = DateTime.Now;
                    existingEntity.CreatedDate = DateTime.Now;
                    existingEntity.CreatedBy = dto.CreatedBy;

                    _dbContext.MstFollowupPosts.Update(existingEntity);
                }
                else
                {
                    // Create new followup post
                    dto.CreatedDate = DateTime.Now;
                    dto.AssignedTime = DateTime.Now;
                    var newEntity = _mapper.Map<MstFollowupPost>(dto);
                    newEntity.AssignedTo = assignedToIds;
                    newEntity.CreatedBy = dto.CreatedBy;
                    _dbContext.MstFollowupPosts.Add(newEntity);

                    // Update related post status to "Assigned"
                    var post = await _dbContext.MstPosts.FindAsync(dto.PostId);
                    if (post != null)
                    {
                        post.Status = 1;
                        _dbContext.MstPosts.Update(post);
                    }
                }

                await _dbContext.SaveChangesAsync();
                return ApiResponseDto<object>.SuccessResponse("Followup post saved successfully", StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<List<MstPostDto>>> GetAssignedPostsAsync(int? userId, int? status = null)
        {
            var logTime = DateTime.UtcNow;
            var loggerName = nameof(GetAssignedPostsAsync);
            var url = "Service:GetAssignedPostsAsync";

            try
            {
                await _loggerService.LogInfo(new LogsDto
                {
                    CreatedOn = logTime,
                    Level = LogLevel.Information.ToString(),
                    Message = $"Fetching assigned posts. UserId: {(userId.HasValue ? userId.ToString() : "All")}",
                    Url = url,
                    StackTrace = Environment.StackTrace,
                    Logger = loggerName
                });

                var assignedPosts = await _dbContext.MstFollowupPosts
                 .Where(x => !string.IsNullOrEmpty(x.AssignedTo))
                 .ToListAsync();

                var userIdStr = userId?.ToString();

                var assignedPostIds = assignedPosts
                    .Where(x => x.AssignedTo.Split(',').Any(id => id.Trim() == userIdStr))
                    .Select(x => x.PostId)
                    .Distinct()
                    .ToList();

                if (!assignedPostIds.Any())
                {
                    return ApiResponseDto<List<MstPostDto>>.ErrorResponse(
                        AppMessages.PostNotFound,
                        StatusCodes.Status404NotFound
                    );
                }

                var postsQuery = _dbContext.MstPosts
          .Include(x => x.MstLikesConfigs)
          .Include(x => x.MstPostComments)
          .Include(x => x.MstPostMedia)
          .Include(f=>f.Facility)
          .Where(p => !p.IsDeleted)
          .Where(p => assignedPostIds.Contains(p.PostId));

                if (status.HasValue)
                {
                    postsQuery = postsQuery.Where(p => p.Status == status.Value);
                }

                var posts = await postsQuery
                    .OrderByDescending(p => p.CreatedAt) // or CreatedOn, based on your actual field
                    .ToListAsync();
                if (!posts.Any())
                {
                    await _loggerService.LogInfo(new LogsDto
                    {
                        CreatedOn = DateTime.UtcNow,
                        Level = LogLevel.Warning.ToString(),
                        Message = "No assigned posts found.",
                        Url = url,
                        Logger = loggerName
                    });

                    return ApiResponseDto<List<MstPostDto>>.ErrorResponse(
                        AppMessages.PostNotFound,
                        StatusCodes.Status404NotFound
                    );
                }

                // Map to DTO and enrich
                var postDtos = _mapper.Map<List<MstPostDto>>(posts);
                for (int i = 0; i < postDtos.Count; i++)
                {
                    var post = posts[i];
                    var dto = postDtos[i];
                    dto.FacilityName = post.Facility?.FacilityName;

                    dto.LikeCount = post.MstLikesConfigs.Count(l => l.IsLiked);
                    dto.PostCommentsCount = post.MstPostComments.Count();
                    dto.ImageBase64 = post.MstPostMedia.FirstOrDefault()?.MediaUrl;
                    dto.AfterMediaUrls = post.MstPostMedia
     .Where(m => !string.IsNullOrWhiteSpace(m.AfterMediaUrl))
     .SelectMany(m => m.AfterMediaUrl.Split(',', StringSplitOptions.RemoveEmptyEntries))
     .Select(url => url.Trim())
     .ToList();
                    dto.IsLikedByUser = userId.HasValue && userId.Value > 0
                        ? post.MstLikesConfigs.Any(l => l.IsLiked && l.UserId == userId.Value)
                        : false;

                    dto.IsAuthor = userId.HasValue && userId.Value > 0 && post.UserId == userId.Value;

                    // Facility name
                    dto.FacilityName = post.Facility?.FacilityName;

                    // Assigned info
                    var assignment = await _dbContext.MstFollowupPosts
                        .FirstOrDefaultAsync(f => f.PostId == post.PostId && f.AssignedTo != null);

                    dto.IsAssigned = assignment != null;

                    if (assignment != null)
                    {
                        var assignedUserIds = assignment.AssignedTo?
                 .Split(',', StringSplitOptions.RemoveEmptyEntries)
                 .Select(id => int.TryParse(id, out var parsedId) ? parsedId : (int?)null)
                 .Where(id => id.HasValue)
                 .Select(id => id.Value)
                 .ToList() ?? new List<int>();

                        // ✅ Get usernames of assigned users
                        dto.AssignedToUsername = await _dbContext.MstUsers
                            .Where(u => assignedUserIds.Contains(u.UserId))
                            .Select(u => u.Username)
                            .ToListAsync();

                        // Assigned By
                        dto.AssignedByUsername = await _dbContext.MstUsers
                            .Where(u => u.UserId == assignment.CreatedBy)
                            .Select(u => u.Username)
                            .FirstOrDefaultAsync();
                        dto.CompletedBy = await _dbContext.MstUsers
               .Where(u => u.UserId == assignment.CompletedBy)
               .Select(u => u.Username)
                .FirstOrDefaultAsync();

                        dto.CompletedComments = assignment.Comments;
                    }
                    else
                    {
                        dto.AssignedToUsername = null;
                        dto.AssignedByUsername = null;
                    }
                }


                await _loggerService.LogInfo(new LogsDto
                {
                    CreatedOn = DateTime.UtcNow,
                    Level = LogLevel.Information.ToString(),
                    Message = $"Assigned posts fetched successfully. Count: {postDtos.Count}",
                    Url = url,
                    Logger = loggerName
                });

                return ApiResponseDto<List<MstPostDto>>.SuccessResponse(
                    AppMessages.PostFetchSuccess,
                    StatusCodes.Status200OK,
                    postDtos
                );
            }
            catch (Exception ex)
            {
                _logger.Error($"[{loggerName}] Exception - {ex.Message}");

                await _loggerService.LogError(new LogsDto
                {
                    CreatedOn = DateTime.UtcNow,
                    Level = LogLevel.Error.ToString(),
                    Message = $"Exception in {loggerName}: {ex.Message}",
                    Url = url,
                    Exception = ex.ToString(),
                    StackTrace = ex.StackTrace,
                    Logger = loggerName
                });

                return ApiResponseDto<List<MstPostDto>>.ErrorResponse(
                    AppMessages.InternalServerError,
                    StatusCodes.Status500InternalServerError
                );
            }
        }
        public async Task<ApiResponseDto<string>> UpdateFollowupStatusAsync(FollowupStatusUpdateRequestDto request)
        {
            var logTime = DateTime.UtcNow;
            var loggerName = nameof(UpdateFollowupStatusAsync);
            var url = "Service:UpdateFollowupStatusAsync";

            try
            {
                var followup = await _dbContext.MstFollowupPosts
                    .FirstOrDefaultAsync(f => f.PostId == request.PostId);

                if (followup == null)
                {
                    return ApiResponseDto<string>.ErrorResponse("Follow-up record not found", StatusCodes.Status404NotFound);
                }

                switch (request.Status)
                {
                    case PostStatus.Followup:
                        followup.FollowedupBy = request.UserId;
                        followup.FollowedupTime = logTime;
                        break;
                    case PostStatus.Completed:
                        followup.CompletedBy = request.UserId;
                        followup.CompletedTime = logTime;
                        followup.Comments = request.CompletedComments;
                        // ✅ Save AfterMediaUrl(s) to PostMedia (comma-separated)
                        if (request.AfterMediaUrl != null && request.AfterMediaUrl.Any())
                        {
                            var existingAfterMedia = await _dbContext.MstPostMedia
                                .FirstOrDefaultAsync(m => m.PostId == request.PostId);

                            if (existingAfterMedia != null)
                            {
                                // Merge old and new URLs, removing duplicates
                                var existingUrls = existingAfterMedia.AfterMediaUrl?
                                    .Split(',', StringSplitOptions.RemoveEmptyEntries)
                                    .Select(u => u.Trim())
                                    .ToList() ?? new List<string>();

                                foreach (var urls in request.AfterMediaUrl)
                                {
                                    if (!string.IsNullOrWhiteSpace(urls) && !existingUrls.Contains(urls.Trim()))
                                    {
                                        existingUrls.Add(urls.Trim());
                                    }
                                }

                                existingAfterMedia.AfterMediaUrl = string.Join(",", existingUrls);
                               
                            }
                            else
                            {
                                _dbContext.MstPostMedia.Add(new MstPostMedium
                                {
                                    PostId = request.PostId,
                                    AfterMediaUrl = string.Join(",", request.AfterMediaUrl.Where(u => !string.IsNullOrWhiteSpace(u)).Select(u => u.Trim())),
                                    CreatedAt = DateTime.UtcNow
                                });
                            }
                        }
                        break;

                    case PostStatus.Archived:
                        followup.ArchivedBy = request.UserId;
                        followup.ArchivedTime = logTime;
                        break;

                    default:
                        return ApiResponseDto<string>.ErrorResponse("Invalid status", StatusCodes.Status400BadRequest);
                }

                // Update the post status
                var post = await _dbContext.MstPosts
                    .FirstOrDefaultAsync(p => p.PostId == request.PostId);

                if (post == null)
                {
                    return ApiResponseDto<string>.ErrorResponse("Post not found", StatusCodes.Status404NotFound);
                }

                post.Status = (int)request.Status;

                await _dbContext.SaveChangesAsync();

                return ApiResponseDto<string>.SuccessResponse("Status updated successfully", StatusCodes.Status200OK, null);
            }
            catch (Exception ex)
            {
                _logger.Error($"[{loggerName}] Exception - {ex.Message}");

                await _loggerService.LogError(new LogsDto
                {
                    CreatedOn = DateTime.UtcNow,
                    Level = LogLevel.Error.ToString(),
                    Message = $"Exception in {loggerName}: {ex.Message}",
                    Url = url,
                    Exception = ex.ToString(),
                    StackTrace = ex.StackTrace,
                    Logger = loggerName
                });

                return ApiResponseDto<string>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> GetAssignedUserAsync(int postId)
        {
            var logTime = DateTime.UtcNow;
            var loggerName = nameof(GetAssignedUserAsync);
            var url = "Service:GetAssignedUserAsync";

            try
            {
                var followup = await _dbContext.MstFollowupPosts
                    .FirstOrDefaultAsync(f => f.PostId == postId);

                if (followup == null)
                {
                    return ApiResponseDto<object>.ErrorResponse("Follow-up record not found", StatusCodes.Status404NotFound);
                }

                var assignedUser = await _dbContext.MstUsers
                    .Where(u => u.UserId == followup.AssignedTo.FirstOrDefault())
                    .Select(u => new
                    {
                        u.UserId,
                        u.Username
                    })
                    .FirstOrDefaultAsync();

                if (assignedUser == null)
                {
                    return ApiResponseDto<object>.ErrorResponse("Assigned user not found", StatusCodes.Status404NotFound);
                }

                return ApiResponseDto<object>.SuccessResponse("Assigned user fetched successfully", StatusCodes.Status200OK, assignedUser);
            }
            catch (Exception ex)
            {
                _logger.Error($"[{loggerName}] Exception - {ex.Message}");

                await _loggerService.LogError(new LogsDto
                {
                    CreatedOn = DateTime.UtcNow,
                    Level = LogLevel.Error.ToString(),
                    Message = $"Exception in {loggerName}: {ex.Message}",
                    Url = url,
                    Exception = ex.ToString(),
                    StackTrace = ex.StackTrace,
                    Logger = loggerName
                });

                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }
        public async Task<ApiResponseDto<object>> DeletePostByIdAsync(int postId, int deletedBy)
        {
            try
            {
                var post = await _dbContext.MstPosts.FindAsync(postId);

                if (post == null)
                {
                    return ApiResponseDto<object>.ErrorResponse("Post not found", StatusCodes.Status404NotFound);
                }

                if (post.IsDeleted)
                {
                    return ApiResponseDto<object>.ErrorResponse("Post is already deleted", StatusCodes.Status400BadRequest);
                }

                // Perform soft delete
                post.IsDeleted = true;
                post.DeletedBy = deletedBy;

                _dbContext.MstPosts.Update(post);
                await _dbContext.SaveChangesAsync();

                return ApiResponseDto<object>.SuccessResponse("Post soft deleted successfully", StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                // Optionally log the exception here
                return ApiResponseDto<object>.ErrorResponse("Internal server error", StatusCodes.Status500InternalServerError);
            }
        }

    }
}
