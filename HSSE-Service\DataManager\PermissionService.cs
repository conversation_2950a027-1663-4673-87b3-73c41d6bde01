using AutoMapper;
using HSSE_Domain.Models;
using HSSE_ModelDto.ModelDto;
using HSSE_Service.InterfaceService;
using HSSE_Service.NLog;
using Microsoft.EntityFrameworkCore;

namespace HSSE_Service.DataManager
{
    public class PermissionService : IPermissionService
    {

        private IMapper _mapper;
        readonly HsseDbLatestContext _context;
        private ILoggerManager _logger;
        private readonly IApiLoggerService _loggerService;

        public PermissionService(IApiLoggerService loggerService, HsseDbLatestContext dbContext, IMapper mapper, ILoggerManager logger)
        {
            _context = dbContext;
            _mapper = mapper;
            _logger = logger;
            _loggerService = loggerService;
        }
        public ApiResponseDto<object> GetPermissionsByUserId(int roleId)
        {
            //var user = _context.MstUsers
            //    .Where(u => u.UserId == userId)
            //    .Select(u => new { u.RoleId })
            //    .FirstOrDefault();

            //if (user == null || user.RoleId == null)
            //    return ApiResponseDto<object>.ErrorResponse("User not found or role missing", 404);

            //int roleId = user.RoleId.Value;

            // Step 1: Get all permissions for this role, including parent info
            var rolePermissions = (
            from perm in _context.MstPermissions
                join rolePerm in _context.MstRoleMenuPermissions
                    on perm.PermissionId equals rolePerm.PermissionId
                where rolePerm.RoleId == roleId
                select new PermissionDto
                {
                    PermissionId = perm.PermissionId,
                    ParentMenuId = perm.ParentMenuId,
                    MenuName = perm.MenuName,
                    ControllerName = perm.ControllerName,
                    ActionName = perm.ActionName,
                    AreaName = perm.AreaName,
                    RouteParams = perm.RouteParams,
                    Icon = perm.Icon,
                    OrderNo = perm.OrderNo,
                    IsActive = perm.IsActive,
                    CanView = rolePerm.CanView,
                    CanCreate = rolePerm.CanCreate,
                    CanEdit = rolePerm.CanEdit,
                    CanDelete = rolePerm.CanDelete
                }
            ).ToList();

            // Step 2: Build a lookup for parent-child hierarchy
            var lookup = rolePermissions.ToLookup(p => p.ParentMenuId);

            List<PermissionDto> BuildTree(int? parentId)
            {
                return lookup[parentId]
                    .OrderBy(p => p.OrderNo)
                    .Select(p => new PermissionDto
                    {
                        PermissionId = p.PermissionId,
                        ParentMenuId = p.ParentMenuId,
                        MenuName = p.MenuName,
                        ControllerName = p.ControllerName,
                        ActionName = p.ActionName,
                        AreaName = p.AreaName,
                        RouteParams = p.RouteParams,
                        Icon = p.Icon,
                        OrderNo = p.OrderNo,
                        IsActive = p.IsActive,
                        CanView = p.CanView,
                        CanCreate = p.CanCreate,
                        CanEdit = p.CanEdit,
                        CanDelete = p.CanDelete,
                        Children = BuildTree(p.PermissionId)
                    })
                    .ToList();
            }

            var permissionTree = BuildTree(null); // top-level menus

            return ApiResponseDto<object>.SuccessResponse("Permissions fetched", 200, permissionTree);
        }

    }
} 