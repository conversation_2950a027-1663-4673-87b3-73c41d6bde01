using HSSE_ModelDto.ModelDto;
using System.Collections.Generic;

namespace HSSE_Service.InterfaceService
{
    public interface IInspectionService
    {
        ApiResponseDto<MstActionPartyDto> CreateOrUpdateActionParty(MstActionPartyDto dto);
        ApiResponseDto<object> GetActionPartyById(int actionPartyId);
        ApiResponseDto<List<MstActionPartyDto>> GetActionPartiesByFacilityId(int facilityId);
        ApiResponseDto<List<ActionPartyViewDto>> GetActionPartiesByUserId(int userId);
        ApiResponseDto<object> ToggleActionPartiesActivation(int actionPartyId);
        ApiResponseDto<MstInspectionDto> CreateOrUpdateInspection(MstInspectionDto dto);
        ApiResponseDto<MstInspectionDto> GetInspectionById(int inspectionId);
        ApiResponseDto<List<MstInspectionDto>> GetInspectionsByActionParty(int actionPartyId, int? status);
        ApiResponseDto<MstInspectionItemDto> UpdateInspectionItemStatus(UpdateInspectionStatusDto req);
        ApiResponseDto<List<MstInspectionCategoryDto>> GetInspectionCategory();
        ApiResponseDto<List<MstInspectionDto>> GetUserInspections(int userId);
    }

}