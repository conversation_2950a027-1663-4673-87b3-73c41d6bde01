using HSSE_ModelDto.ModelDto;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.IO;

namespace HSSE_API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class NewsLetterController : ControllerBase
    {
        private readonly INewsLetterService _service;
        private readonly IConfiguration _configuration;

        public NewsLetterController(IConfiguration configuration, INewsLetterService service)
        {
            _service = service;
            _configuration = configuration;
        }

        [HttpPost]
        public IActionResult InsertOrUpdateNewsLetter([FromBody] MstNewsletterDto dto)
        {
            try
            {
                if (!string.IsNullOrEmpty(dto.ThumbnailPath) && !string.IsNullOrEmpty(dto.FileName))
                {
                    // Save in external folder on C: drive
                    string externalRootPath = @"C:\HSSE-Announcements";
                    string uploadFolder = Path.Combine(externalRootPath, "newsletter-images");

                    if (!Directory.Exists(uploadFolder))
                    {
                        Directory.CreateDirectory(uploadFolder);
                    }

                    string uniqueFileName = $"{Guid.NewGuid()}_{dto.FileName}";
                    string filePath = Path.Combine(uploadFolder, uniqueFileName);

                    // Strip base64 prefix if exists
                    string base64Data = dto.ThumbnailPath;
                    if (base64Data.Contains(","))
                    {
                        base64Data = base64Data.Substring(base64Data.IndexOf(",") + 1);
                    }

                    byte[] fileBytes = Convert.FromBase64String(base64Data);
                    System.IO.File.WriteAllBytes(filePath, fileBytes);

                    // Save virtual download path
                    dto.ThumbnailPath = $"/ExternalFiles/newsletter-images/{uniqueFileName}";
                }

                var result = _service.InsertOrUpdateNewsLetter(dto);
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpGet]
        public IActionResult GetNewsLettersByUserId(int userId)
        {
            var result = _service.GetNewsLettersByUserId(userId);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public IActionResult GetNewsLettersById(int newsletterId)
        {
            var result = _service.GetNewsLettersById(newsletterId);
            return StatusCode(result.Status, result);
        }
        [HttpGet]
        public IActionResult GetNewsLettersByFacilityId(int facilityId, int userId)
        {
            var result = _service.GetNewsLettersByFacilityId(facilityId, userId);
            return StatusCode(result.Status, result);
        }

        [HttpDelete]
        public IActionResult DeleteNewsLetter(int newsLetterId)
        {
            var result = _service.DeleteNewsLetter(newsLetterId);
            return StatusCode(result.Status, result);
        }

        [HttpPost]
        public IActionResult ToggleNewsLetterActivation(int newsLetterId)
        {
            var result = _service.ToggleNewsLetterActivation(newsLetterId);
            return StatusCode(result.Status, result);
        }
    }
} 