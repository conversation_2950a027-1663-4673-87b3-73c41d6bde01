using AutoMapper;
using Azure.Core;
using HSSE_Domain.Models;
using HSSE_ModelDto.ModelDto;
using HSSE_Service.Constants;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;

namespace HSSE_Service.DataManager
{
    public class InspectionService : IInspectionService
    {
        private readonly HsseDbLatestContext _dbContext;
        private readonly IMapper _mapper;

        public InspectionService(HsseDbLatestContext dbContext, IMapper mapper)
        {
            _dbContext = dbContext;
            _mapper = mapper;
        }
        public ApiResponseDto<MstActionPartyDto> CreateOrUpdateActionParty(MstActionPartyDto dto)
        {
            try
            {
                if (dto.ActionPartyId > 0)
                {
                    var entity = await _dbContext.MstActionParties
                        .Include(x => x.MstActionPartyUserMappings)
                        .FirstOrDefaultAsync(x => x.ActionPartyId == dto.ActionPartyId);

                    if (entity == null)
                        return ApiResponseDto<MstActionPartyDto>.ErrorResponse("ActionParty not found", 404);

                    // Manual property mapping (excluding navigation collections)
                    entity.Name = dto.Name;
                    entity.FacilityId = dto.FacilityId;
                    entity.IsActive = dto.IsActive;
                    entity.ModifiedAt = DateTime.UtcNow;
                    entity.ModifiedBy = dto.ModifiedBy;

                    // Remove old mappings
                    _dbContext.MstActionPartyUserMappings.RemoveRange(entity.MstActionPartyUserMappings);

                    // Add new mappings
                    if (dto.UserMappings != null && dto.UserMappings.Any())
                    {
                        entity.MstActionPartyUserMappings = dto.UserMappings.Select(mapping => new MstActionPartyUserMapping
                        {
                            UserId = mapping.UserId,
                            ActionPartyId = entity.ActionPartyId // set explicitly
                        }).ToList();
                    }

                    _dbContext.MstActionParties.Update(entity);
                }

                else
                {
                    var entity = _mapper.Map<MstActionParty>(dto);
                    entity.CreatedAt = DateTime.UtcNow;

                    if (dto.UserMappings != null && dto.UserMappings.Any())
                    {
                        entity.MstActionPartyUserMappings = dto.UserMappings.Select(mapping => new MstActionPartyUserMapping
                        {
                            UserId = mapping.UserId
                        }).ToList();
                    }

                    _dbContext.MstActionParties.Add(entity);
                }

                await _dbContext.SaveChangesAsync();
                return ApiResponseDto<MstActionPartyDto>.SuccessResponse("Success", 200);
            }
            catch (Exception ex)
            {
                // Optionally log the exception
                return ApiResponseDto<MstActionPartyDto>.ErrorResponse("Internal server error", 500);
            }
        }

        public async Task<ApiResponseDto<object>> GetActionPartyByIdAsync(int actionPartyId)
        {
            try
            {
                var entity = await _dbContext.MstActionParties
                    .Include(x => x.Facility) // Ensure Facility is included
                    .Include(x => x.MstActionPartyUserMappings)
                        .ThenInclude(m => m.User)
                    .Where(x => x.ActionPartyId == actionPartyId)
                    .Select(x => new
                    {
                        ActionPartyId = x.ActionPartyId,
                        ActionPartyName = x.Name,
                        FacilityId = x.FacilityId,
                        UserIds = x.MstActionPartyUserMappings.Select(m => m.UserId).ToList(),
                        IsActive = x.IsActive
                    })
                    .FirstOrDefaultAsync();

                if (entity == null)
                    return ApiResponseDto<object>.ErrorResponse("Not found", 404);

                return ApiResponseDto<object>.SuccessResponse("Success", 200, entity);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse("Internal server error", 500);
            }
        }

        public async Task<ApiResponseDto<List<MstActionPartyDto>>> GetActionPartiesByFacilityIdAsync(int facilityId)
        {
            try
            {
                var entities = await _dbContext.MstActionParties.Include(x => x.MstActionPartyUserMappings).Where(x => x.FacilityId == facilityId && x.IsActive).ToListAsync();
                var dtos = _mapper.Map<List<MstActionPartyDto>>(entities);
                return ApiResponseDto<List<MstActionPartyDto>>.SuccessResponse("Success", 200, dtos);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstActionPartyDto>>.ErrorResponse("Internal server error", 500);
            }
        }

        public async Task<ApiResponseDto<List<ActionPartyViewDto>>> GetActionPartiesByUserIdAsync(int userId)
        {
            try
            {
                var result = await _dbContext.MstActionParties
                    .Include(x => x.MstActionPartyUserMappings)
                    .Where(x => x.CreatedBy == userId)
                    .Select(x => new ActionPartyViewDto
                    {
                        ActionPartyId = x.ActionPartyId,
                        ActionPartyName = x.Name,
                        FacilityName = x.Facility.FacilityName,
                        UserNames = string.Join(", ", x.MstActionPartyUserMappings.Select(u => u.User.Username)),
                        IsActive = x.IsActive
                    })
                    .ToListAsync();

                return ApiResponseDto<List<ActionPartyViewDto>>.SuccessResponse("Success", 200, result);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<ActionPartyViewDto>>.ErrorResponse("Internal server error", 500);
            }
        }
        public async Task<ApiResponseDto<object>> ToggleActionPartiesActivationAsync(int actionPartyId)
        {
            try
            {
                var entity = await _dbContext.MstActionParties.FirstOrDefaultAsync(a=>a.ActionPartyId == actionPartyId);
                if (entity == null)
                {
                    return ApiResponseDto<object>.ErrorResponse("Data Not Found", StatusCodes.Status404NotFound, new List<Error>
                    {
                        new Error { Code = 404, Message = AppMessages.EventNotFound }
                    });
                }

                // Toggle the IsActive status
                entity.IsActive = !entity.IsActive;
                await _dbContext.SaveChangesAsync();

                var message = entity.IsActive ? AppMessages.ActionPartiesActivateSuccess : AppMessages.ActionPartiesDeActivateSuccess;
                return ApiResponseDto<object>.SuccessResponse(message, StatusCodes.Status200OK, entity.IsActive);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError, new List<Error>
                {
                    new Error { Code = 500, Message = ex.Message }
                });
            }
        }

        public async Task<ApiResponseDto<MstInspectionDto>> CreateOrUpdateInspectionAsync(MstInspectionDto dto)
        {
            try
            {
                MstInspection entity;
                int categoryId = dto.TypeOfInspection ?? 0;

                if (!string.IsNullOrWhiteSpace(dto.TypeOfInspectionName) && categoryId == 0)
                {
                    var newCategory = new MstInspectionCategory // replace with your actual Category entity
                    {
                        CategoryName = dto.TypeOfInspectionName,
                        CreatedAt = DateTime.UtcNow,
                        Status = true
                    };

                    _dbContext.MstInspectionCategories.Add(newCategory);
                    await _dbContext.SaveChangesAsync();

                    // Set the new category ID to use for the announcement
                    categoryId = newCategory.InspectionCategoryId; // Adjust property name to your model's PK
                }

                if (dto.InspectionId > 0)
                {
                    // Update existing inspection
                    entity = await _dbContext.MstInspections
                        .Include(x => x.MstInspectionItems)
                        .FirstOrDefaultAsync(x => x.InspectionId == dto.InspectionId);

                    if (entity == null)
                        return ApiResponseDto<MstInspectionDto>.ErrorResponse("Inspection not found", 404);

                    // Update only allowed fields
                    entity.FacilityId = dto.FacilityId;
                    entity.ModifiedAt = DateTime.UtcNow;

                    // Update inspection item � assuming single item per inspection
                    var inspecItems = entity.MstInspectionItems.FirstOrDefault();
                    if (inspecItems != null)
                    {
                        inspecItems.Description = dto.Description;
                        inspecItems.TypeOfInspection = categoryId;
                        inspecItems.ActionPartyName = dto.ActionPartyId;
                        inspecItems.ContactPersonId = dto.ContactPersonId;
                        inspecItems.SpecificLocation = dto.SpecificLocation;
                        inspecItems.Observation = dto.Observation;
                        inspecItems.Recommendation = dto.Recommendation;
                        inspecItems.ModifiedAt = DateTime.UtcNow;

                        if (!string.IsNullOrEmpty(dto.ObservationAttachmentBase64))
                            inspecItems.ObservationMediaUrl = dto.ObservationAttachmentBase64;

                        if (!string.IsNullOrEmpty(dto.RecommendationAttachmentBase64))
                            inspecItems.RecommendationMediaUrl = dto.RecommendationAttachmentBase64;

                        if (!string.IsNullOrEmpty(dto.AfterImagePath))
                            inspecItems.AfterImagePath = dto.AfterImagePath;
                    }
                    else
                    {
                        // Add new item if not present (fallback)
                        entity.MstInspectionItems = new List<MstInspectionItem>
                {
                    new MstInspectionItem
                    {
                        InspectionId = entity.InspectionId,
                        Description = dto.Description,
                        TypeOfInspection = categoryId,
                        ActionPartyName = dto.ActionPartyId,
                        Verification = dto.Verification,
                        Status = dto.Status,
                        SpecificLocation = dto.SpecificLocation,
                        Observation = dto.Observation,
                        Recommendation = dto.Recommendation,
                        ObservationMediaUrl = dto.ObservationAttachmentBase64,
                        RecommendationMediaUrl = dto.RecommendationAttachmentBase64,
                        AfterImagePath = dto.AfterImagePath,
                        CreatedAt = DateTime.UtcNow
                    }
                };
                    }
                }
                else
                {
              
                    // Insert case
                    entity = new MstInspection
                    {
                        FacilityId = dto.FacilityId,
                        Title = dto.Title,
                        Description = dto.Description,
                        InspectionDate = dto.InspectionDate ?? DateTime.UtcNow,
                        ReferenceNo = dto.ReferenceNo,
                        CreatedBy = dto.CreatedBy,
                        CreatedAt = DateTime.UtcNow,
                        ModifiedAt = null
                    };

                    _dbContext.MstInspections.Add(entity);
                    await _dbContext.SaveChangesAsync();

                    var inspectionItem = new MstInspectionItem
                    {
                        InspectionId = entity.InspectionId,
                        SpecificLocation = dto.SpecificLocation,
                        Description = dto.Description,
                        TypeOfInspection = categoryId,
                        ActionPartyName = dto.ActionPartyId,
                        Verification = dto.Verification,
                        Status = dto.Status,
                        Observation = dto.Observation,
                        Recommendation = dto.Recommendation,
                        ObservationMediaUrl = dto.ObservationAttachmentBase64,
                        RecommendationMediaUrl = dto.RecommendationAttachmentBase64,
                        AfterImagePath = dto.AfterImagePath,
                        Rectification = dto.Rectification,
                        ContactPersonId = dto.ContactPersonId,
                        CompletionDateTime = dto.CompletionDateTime,
                        CreatedAt = DateTime.UtcNow,
                        ModifiedAt = null
                    };

                    _dbContext.MstInspectionItems.Add(inspectionItem);
                }

                await _dbContext.SaveChangesAsync();

                return ApiResponseDto<MstInspectionDto>.SuccessResponse("Success", 200);
            }
            catch (Exception ex)
            {
                // log ex if needed
                return ApiResponseDto<MstInspectionDto>.ErrorResponse("Internal server error", 500);
            }
        }

        public async Task<ApiResponseDto<MstInspectionDto>> GetInspectionByIdAsync(int inspectionId)
        {
            try
            {
                var entity = await _dbContext.MstInspections.Include(x => x.MstInspectionItems).FirstOrDefaultAsync(x => x.InspectionId == inspectionId);
                if (entity == null)
                    return ApiResponseDto<MstInspectionDto>.ErrorResponse("Inspection not found", 404);
                var dto = _mapper.Map<MstInspectionDto>(entity);
                return ApiResponseDto<MstInspectionDto>.SuccessResponse("Success", 200, dto);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<MstInspectionDto>.ErrorResponse("Internal server error", 500);
            }
        }

        public async Task<ApiResponseDto<List<MstInspectionDto>>> GetInspectionsByActionPartyAsync(int actionPartyId, int? status)
        {
            try
            {
                var inspections = await _dbContext.MstInspections
             .Include(i => i.MstInspectionItems)
             .Where(i => i.MstInspectionItems.Any(item =>
                 item.ActionPartyName == actionPartyId &&
                 (
                     (status == null && item.Status != 1) ||
                     (status != null && item.Status == status)
                 )
             ))
             .ToListAsync();
                if (!inspections.Any())
                    return ApiResponseDto<List<MstInspectionDto>>.ErrorResponse("Inspection not found", 404);
                // Get all unique user IDs from inspections
                var createdByUserIds = inspections.Select(i => i.CreatedBy).Distinct().ToList();
                var contactPersonIds = inspections?.FirstOrDefault()?.MstInspectionItems.Select(i => i.ContactPersonId).Distinct().ToList();
                var inspectionsType = await _dbContext.MstInspectionCategories.ToDictionaryAsync(u => u.InspectionCategoryId, u => u.CategoryName);
                // Fetch usernames in a single DB call for better performance
                var userMap = await _dbContext.MstUsers
                    .Where(u => createdByUserIds.Contains(u.UserId) || contactPersonIds.Contains(u.UserId))
                    .ToDictionaryAsync(u => u.UserId, u => u.FirstName);

                // Now project the data
                var result = inspections
                    .SelectMany(ins => ins.MstInspectionItems
                        .Where(item => item.ActionPartyName == actionPartyId)
                        .Select(item => new MstInspectionDto
                        {
                            InspectionId = ins.InspectionId,
                            FacilityId = ins.FacilityId,
                            Title = ins.Title,
                            Description = ins.Description,
                            InspectionDate = ins.InspectionDate,
                           AfterImagePath = item.AfterImagePath,
                            ReferenceNo = ins.ReferenceNo,
                            TypeOfInspectionName = inspectionsType.TryGetValue(item.TypeOfInspection ?? 0, out var categoryName) ? categoryName : null,
                            ActionPartyId = item.ActionPartyName,
                            SpecificLocation = item.SpecificLocation,
                            InspectorName = userMap.ContainsKey(ins.CreatedBy ?? 0) ? userMap[ins.CreatedBy ?? 0] : null,
                            Verification = item.Verification,
                            Rectification = item.Rectification,
                            CreatedBy = ins.CreatedBy,
                            CreatedAt = ins.CreatedAt,
                            ModifiedAt = ins.ModifiedAt,
                            CompletionDateTime = item.CompletionDateTime,
                            Observation = item.Observation,
                            Recommendation = item.Recommendation,
                            Status = item.Status,
                            ObservationAttachmentBase64 = item.ObservationMediaUrl,
                            RecommendationAttachmentBase64 = item.RecommendationMediaUrl,
                            ContactPersonName = userMap.ContainsKey(item.ContactPersonId ?? 0) ? userMap[item.ContactPersonId ?? 0] : null,
                        }))
                    .ToList();

                return ApiResponseDto<List<MstInspectionDto>>.SuccessResponse("Success", 200, result);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstInspectionDto>>.ErrorResponse("Internal server error", 500);
            }
        }
        public async Task<ApiResponseDto<List<MstInspectionDto>>> GetUserInspectionsAsync(int userId)
        {
            try
            {
                var inspections = await _dbContext.MstInspections
             .Include(i => i.MstInspectionItems).Where(u => u.CreatedBy == userId).ToListAsync();
          
         
                if (!inspections.Any())
                    return ApiResponseDto<List<MstInspectionDto>>.ErrorResponse("Inspection not found", 404);
                // Get all unique user IDs from inspections
                var createdByUserIds = inspections.Select(i => i.CreatedBy).Distinct().ToList();
                var contactPersonIds = inspections?.FirstOrDefault()?.MstInspectionItems.Select(i => i.ContactPersonId).Distinct().ToList();
                var inspectionsType = await _dbContext.MstInspectionCategories.ToDictionaryAsync(u => u.InspectionCategoryId, u => u.CategoryName);
                // Fetch usernames in a single DB call for better performance
                var userMap = await _dbContext.MstUsers
                    .Where(u => createdByUserIds.Contains(u.UserId) || contactPersonIds.Contains(u.UserId))
                    .ToDictionaryAsync(u => u.UserId, u => u.FirstName);

                // Now project the data
                var result = inspections
                    .SelectMany(ins => ins.MstInspectionItems
                        .Select(item => new MstInspectionDto
                        {
                            InspectionId = ins.InspectionId,
                            FacilityId = ins.FacilityId,
                            Title = ins.Title,
                            Description = ins.Description,
                            InspectionDate = ins.InspectionDate,
                            AfterImagePath = item.AfterImagePath,
                            ReferenceNo = ins.ReferenceNo,
                            TypeOfInspectionName = inspectionsType.TryGetValue(item.TypeOfInspection ?? 0, out var categoryName) ? categoryName : null,
                            TypeOfInspection = item.TypeOfInspection,
                            ActionPartyId = item.ActionPartyName,
                            SpecificLocation = item.SpecificLocation,
                            InspectorName = userMap.ContainsKey(ins.CreatedBy ?? 0) ? userMap[ins.CreatedBy ?? 0] : null,
                            Verification = item.Verification,
                            Rectification = item.Rectification,
                            CreatedBy = ins.CreatedBy,
                            CreatedAt = ins.CreatedAt,
                            ModifiedAt = ins.ModifiedAt,
                            CompletionDateTime = item.CompletionDateTime,
                            Observation = item.Observation,
                            Recommendation = item.Recommendation,
                            Status = item.Status,
                            ObservationAttachmentBase64 = item.ObservationMediaUrl,
                            RecommendationAttachmentBase64 = item.RecommendationMediaUrl,
                            ContactPersonName = userMap.ContainsKey(item.ContactPersonId ?? 0) ? userMap[item.ContactPersonId ?? 0] : null,
                            ContactPersonId = item.ContactPersonId
                        }))
                    .ToList();

                return ApiResponseDto<List<MstInspectionDto>>.SuccessResponse("Success", 200, result);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstInspectionDto>>.ErrorResponse("Internal server error", 500);
            }
        }

        public async Task<ApiResponseDto<MstInspectionItemDto>> UpdateInspectionItemStatusAsync(UpdateInspectionStatusDto req)
        {
            try
            {
                var item = await _dbContext.MstInspectionItems
                    .Where(x => x.InspectionId == req.InspectionId)
                    .Select(x => new
                    {
                        Entity = x,
                        x.ItemId,
                        x.Status,
                        x.Description,
                        x.AfterImagePath,
                        x.Verification,
                    })
                    .FirstOrDefaultAsync();

                if (item == null)
                    return ApiResponseDto<MstInspectionItemDto>.ErrorResponse("Inspection item not found", 404);

                var entity = item.Entity;

                // If IsVerified is true, only update the Verification field
                if (req.isVerified)
                {
                    entity.Verification = req.Status;
                    entity.ModifiedAt = DateTime.UtcNow;

                    await _dbContext.SaveChangesAsync();

                    return ApiResponseDto<MstInspectionItemDto>.SuccessResponse("Verification status updated", 200);
                }

                // Track changes for audit
                bool isModified = false;
                var audit = new MstInspectionItemAudit
                {
                    ItemId = item.ItemId,
                    OldStatus = item.Status?.ToString(),
                    OldRectification = item.Description,
                    OldAfterImagePath = item.AfterImagePath,
                    ChangedBy = req.ChangedBy,
                    ChangedAt = DateTime.UtcNow
                };

                // Update status
                if (entity.Status != req.Status)
                {
                    entity.Status = req.Status;
                    audit.NewStatus = req.Status.ToString();
                    isModified = true;
                }

                // Update after image path
                if (!string.IsNullOrEmpty(req.AfterImageUrl) && req.AfterImageUrl != item.AfterImagePath)
                {
                    entity.AfterImagePath = req.AfterImageUrl;
                    audit.NewAfterImagePath = req.AfterImageUrl;
                    isModified = true;
                }

                // Update rectification / remarks
                if (!string.IsNullOrEmpty(req.Remarks) && req.Remarks != item.Description)
                {
                    entity.Rectification = req.Remarks;
                    audit.NewRectification = req.Remarks;
                    isModified = true;
                }

                // Update completion date
                if (req.CompletionDate.HasValue)
                {
                    entity.CompletionDateTime = req.CompletionDate.Value;
                    isModified = true;
                }

                if (isModified)
                {
                    entity.ModifiedAt = DateTime.UtcNow;
                    _dbContext.MstInspectionItemAudits.Add(audit);
                    await _dbContext.SaveChangesAsync();
                }

                return ApiResponseDto<MstInspectionItemDto>.SuccessResponse("Status updated", 200);
            }
            catch (Exception ex)
            {
                // Log exception if needed
                return ApiResponseDto<MstInspectionItemDto>.ErrorResponse("Internal server error", 500);
            }
        }

        public async Task<ApiResponseDto<List<MstInspectionCategoryDto>>> GetInspectionCategoryAsync()
        {
            try
            {
                var entity = await _dbContext.MstInspectionCategories.Where(x=> x.Status == true).ToListAsync();
                if (entity == null || !entity.Any())
                    return ApiResponseDto<List<MstInspectionCategoryDto>>.ErrorResponse("Category not found", 404);

                var dtoList = _mapper.Map<List<MstInspectionCategoryDto>>(entity);
                return ApiResponseDto<List<MstInspectionCategoryDto>>.SuccessResponse("Success", 200, dtoList);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<List<MstInspectionCategoryDto>>.ErrorResponse("Internal server error", 500);
            }
        }
    }
} 