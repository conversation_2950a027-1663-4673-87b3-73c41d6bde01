using HSSE_ModelDto.ModelDto;
using System.Collections.Generic;

namespace HSSE_Service.InterfaceService
{
    public interface IEventService
    {
        ApiResponseDto<object> InsertOrUpdateEvent(MstEventDto eventDto);
        ApiResponseDto<object> GetEvents(int userId);
        ApiResponseDto<object> GetEventById(int eventId);
        //ApiResponseDto<object> GetEventsByFacilityId(int facilityId, int userId);
        ApiResponseDto<object> SaveEventResponse(MstEventResponseDto responseDto);
        ApiResponseDto<object> GetEventResponsesByEventId(int eventId);
        ApiResponseDto<object> SaveEventLike(int eventId, int userId);
        ApiResponseDto<object> GetEventLikesByEventId(int eventId);
        ApiResponseDto<object> ToggleEventActivation(int eventId);
        ApiResponseDto<object> GetEventsByFacilityId(
          int facilityId,
          int userId,
          string? search = null,
    int dateFilterId = 0     // pass the filter id from frontend
      );
        ApiResponseDto<object> GetEventRsvpDetails(int eventId);
    }
}