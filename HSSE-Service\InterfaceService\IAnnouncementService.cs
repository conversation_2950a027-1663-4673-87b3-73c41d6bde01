﻿using HSSE_ModelDto.ModelDto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace HSSE_Service.InterfaceService
{
    public interface IAnnouncementService
    {
        ApiResponseDto<object> InsertOrUpdateGroup(MstGroupDto groupDto);
        ApiResponseDto<object> GetGroups();
        ApiResponseDto<object> GetGroupById(int groupId);
        ApiResponseDto<object> DeleteGroup(int groupId);

        ApiResponseDto<object> InsertOrUpdateGroupMember(MstGroupMemberDto groupMemberDto);
        ApiResponseDto<object> GetGroupMembers();
        ApiResponseDto<object> GetGroupMemberById(int groupMemberId);
        ApiResponseDto<object> DeleteAllGroupMembersByGroupId(int groupId);
        ApiResponseDto<object> GetGroupMembersByGroupId(int groupId);
        ApiResponseDto<object> InsertGroupMembers(MstGroupMemberDto groupMemberDto);
        ApiResponseDto<object> UpdateGroupMembers(MstGroupMemberDto groupMemberDto);


        ApiResponseDto<object> InsertOrUpdateAnnouncement(CreateAnnouncementRequest request);
        ApiResponseDto<object> GetAnnouncements();
        ApiResponseDto<object> GetAnnouncementById(int announcementId);
        ApiResponseDto<object> DeleteAnnouncement(int announcementId);
        ApiResponseDto<object> UpdateAnnouncement(CreateAnnouncementRequest request);
        ApiResponseDto<object> GetUsersAnnouncementsByUserId(int userId);

        ApiResponseDto<object> InsertAnnouncementReceiver(MstAnnouncementReceiverDto dto);
        ApiResponseDto<IEnumerable<object>> GetAnnouncementReceivers(int? userId = null, int? groupId = null, bool? delivered = null);
        ApiResponseDto<object> GetAnnouncementsByUserId(int userId);
        ApiResponseDto<object> GetAnnouncementDetailsById(int announcementId);
        ApiResponseDto<object> ToggleAnnouncement(int announcementId, int statusId);

        ApiResponseDto<object> InsertOrUpdateAnnouncementCategory(MstAnnoucementCategoryDto dto);
        ApiResponseDto<object> GetAnnouncementCategoriesByUserId(int userId);
        ApiResponseDto<object> GetAnnouncementCategoryById(int categoryId);
        //ApiResponseDto<object> DeleteAnnouncementCategory(int categoryId);
        ApiResponseDto<object> ToggleAnnouncementCategoryStatus(int categoryId);
        ApiResponseDto<object> GetAnnouncementCategories();
    }
}
