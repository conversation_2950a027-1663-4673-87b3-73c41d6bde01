﻿using HSSE_API.NLog;
using HSSE_ModelDto.ModelDto;
using HSSE_Service.InterfaceService;
using Microsoft.AspNetCore.Mvc;

namespace HSSE_API.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class LoginController : ControllerBase
    {

        private ILog _logger;
        private string? controllerName;
        private IConfiguration _configuration;
        private readonly ILoginService _service;
        private string? actionName;
        public LoginController(IConfiguration configuration, ILog logger, ILoginService service)
        {
            _logger = logger;
            _service = service;
            _configuration = configuration;
        }
        [HttpPost]
        public IActionResult ValidateToken(LoginRequestDto loginDto)
        {
            _logger.Information($"[SsoLogin] Request received - Username: {loginDto}");

            try
            {
                var result = _service.Login(loginDto);

                if (result.Success)
                {
                    _logger.Information($"[SsoLogin] Login successful {result.Data}");
                    return StatusCode(result.Status, result);
                }

                _logger.Warning($"[SsoLogin] Login failed - Message: {result.Message}");
                return StatusCode(result.Status, result);
            }
            catch (Exception ex)
            {
                _logger.Error($"[SsoLogin] Exception occurred - {ex.Message}");
                var error = ApiResponseDto<object>.ErrorResponse("An unexpected error occurred.", StatusCodes.Status500InternalServerError);
                return StatusCode(StatusCodes.Status500InternalServerError, error);
            }
        }

        [HttpGet]
        public IActionResult GetUserFacility(int userId)
        {
            var result = _service.GetUserFacility(userId);
            return StatusCode(result.Status, result);
        }

        [HttpGet]
        public IActionResult GetUserByFacilityId(int facilityId)
        {
            var result = _service.GetUserByFacilityId(facilityId);
            return StatusCode(result.Status, result);
        }
    }
}
