using AutoMapper;
using HSSE_Domain.Models;
using HSSE_ModelDto.Enum;
using HSSE_ModelDto.ModelDto;
using HSSE_Service.Constants;
using HSSE_Service.InterfaceService;
using HSSE_Service.NLog;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;

namespace HSSE_Service.DataManager
{
    public class EventService : IEventService
    {
        private readonly HsseDbLatestContext _context;
        private readonly IMapper _mapper;
        private readonly ILoggerManager _logger;
        private readonly IConfiguration _configuration;

        public EventService(IConfiguration configuration, HsseDbLatestContext context, IMapper mapper, ILoggerManager logger)
        {
            _context = context;
            _mapper = mapper;
            _logger = logger;
            _configuration = configuration;
        }
        public ApiResponseDto<object> InsertOrUpdateEvent(MstEventDto eventDto)
        {
            try
            {
                MstEvent entity;
                bool isUpdate = eventDto.EventId > 0;

                if (isUpdate)
                {
                    entity = _context.MstEvents.Find(eventDto.EventId);
                    if (entity == null)
                    {
                        return ApiResponseDto<object>.ErrorResponse(AppMessages.EventNotFound, StatusCodes.Status404NotFound);
                    }

                    _mapper.Map(eventDto, entity);
                    _context.MstEvents.Update(entity);

                    // Remove old group mappings
                    var existingReceivers = _context.MstAnnouncementReceivers.Where(r => r.AnnouncementId == eventDto.EventId);
                    _context.MstAnnouncementReceivers.RemoveRange(existingReceivers);
                }
                else
                {
                    entity = _mapper.Map<MstEvent>(eventDto);
                    _context.MstEvents.Add(entity);
                }

                _context.SaveChanges();

                // Insert new group mappings
                if (eventDto.GroupId != null && eventDto.GroupId.Any())
                {
                    var receivers = eventDto.GroupId.Select(groupId => new MstAnnouncementReceiver
                    {
                        EventId = entity.EventId,
                        GroupId = groupId,
                        Delivered = false
                    }).ToList();

                    _context.MstAnnouncementReceivers.AddRange(receivers);
                    _context.SaveChanges();
                }

                return ApiResponseDto<object>.SuccessResponse(
                    isUpdate ? AppMessages.EventUpdatedSuccessfully : AppMessages.EventCreatedSuccessfully,
                    StatusCodes.Status200OK,
                  null);
            }
            catch (Exception ex)
            {
                _logger.Error($"[InsertOrUpdateEvent] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }


        public ApiResponseDto<object> GetEvents(int userId)
        {
            try
            {
                var events = _context.MstEvents.Include(e => e.MstEventResponses).Include(e => e.MstLikesConfigs).Where(x=>x.CreatedBy == userId).ToList();
                var eventDtos = _mapper.Map<List<MstEventDto>>(events);
                return ApiResponseDto<object>.SuccessResponse(AppMessages.EventRetrieved, StatusCodes.Status200OK, eventDtos.Select(e => new {
                    e.EventId,
                    e.Title,
                    e.Description,
                    e.MediaUrl,
                    e.EventDateTime,
                    e.Location,
                    e.ExternalLink,
                    e.CreatedAt,
                    e.CreatedBy,
                    e.FacilityId,
                    e.LikeCount,
                    e.IsActive,
                    e.MstEventResponses,
                    e.IsRsvp
                }));
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetEvents] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }

        public ApiResponseDto<object> GetEventById(int eventId)
        {
            try
            {
                var entity = _context.MstEvents.Include(e => e.MstEventResponses).Include(e => e.MstLikesConfigs).FirstOrDefault(e => e.EventId == eventId);
                if (entity == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.EventNotFound, StatusCodes.Status404NotFound);
                }
                var dto = _mapper.Map<MstEventDto>(entity);
                // Manually count likes from the database
                int likeCount = _context.MstLikesConfigs.Count(l => l.EventId == eventId && l.IsLiked);
                dto.LikeCount = likeCount;
                return ApiResponseDto<object>.SuccessResponse(AppMessages.EventRetrieved, StatusCodes.Status200OK, new {
                    dto.EventId,
                    dto.Title,
                    dto.Description,
                    dto.MediaUrl,
                    dto.EventDateTime,
                    dto.Location,
                    dto.ExternalLink,
                    dto.CreatedAt,
                    dto.CreatedBy,
                    dto.FacilityId,
                    dto.LikeCount,
                    dto.MstEventResponses,
                    dto.IsRsvp
                });
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetEventById] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }

        //public async Task<ApiResponseDto<object>> GetEventsByFacilityIdAsync(int facilityId, int userId)
        //{
        //    try
        //    {
        //        var events = await _context.MstEvents
        //            .Include(e => e.MstEventResponses)
        //            .Include(e => e.MstLikesConfigs)
        //            .Where(e => e.FacilityId == facilityId && e.CreatedBy != userId && e.IsActive)
        //            .ToListAsync();

        //        var eventDtos = _mapper.Map<List<MstEventDto>>(events);

        //        // Manually count likes for each event
        //        // Add LikeCount, IsLikedByUser, and IsAcceptedByUser per event
        //        var result = eventDtos.Select(dto =>
        //        {
        //            var originalEvent = events.FirstOrDefault(e => e.EventId == dto.EventId);
        //            int likeCount = originalEvent?.MstLikesConfigs?.Count ?? 0;
        //            bool isLikedByUser = originalEvent?.MstLikesConfigs?.Any(like => like.UserId == userId) ?? false;

        //            // Check if the user has accepted the event
        //            // Check if the user has accepted the event
        //            bool? isAcceptedByUser = originalEvent?.MstEventResponses?
        //     .FirstOrDefault(r => r.UserId == userId)
        //                    ?.IsAccepted;

        //            return new
        //            {
        //                dto.EventId,
        //                dto.Title,
        //                dto.Description,
        //                dto.MediaUrl,
        //                dto.EventDateTime,
        //                dto.Location,
        //                dto.ExternalLink,
        //                dto.CreatedAt,
        //                dto.CreatedBy,
        //                dto.FacilityId,
        //                LikeCount = likeCount,
        //                IsLikedByUser = isLikedByUser,
        //                IsAcceptedByUser = isAcceptedByUser,  // Add the acceptance status
        //                dto.MstEventResponses
        //            };
        //        }).ToList();

        //        return ApiResponseDto<object>.SuccessResponse(AppMessages.EventRetrieved, StatusCodes.Status200OK, result);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.Error($"[GetEventsByFacilityIdAsync] Exception occurred - {ex.Message}");
        //        return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
        //    }
        //}
        public ApiResponseDto<object> GetEventsByFacilityId(
          int facilityId,
          int userId,
          string? search = null,
    int dateFilterId = 0     // pass the filter id from frontend
      )
        {
            try
            {
                // Parse the int to enum safely:
                EventDateFilter dateFilter = Enum.IsDefined(typeof(EventDateFilter), dateFilterId)
                                             ? (EventDateFilter)dateFilterId
                                             : EventDateFilter.None;

                var userGroupIds = _context.MstGroupMembers
                    .Where(gu => gu.UserId == userId)
                    .Select(gu => gu.GroupId)
                    .ToList();

                var validEventIds = _context.MstAnnouncementReceivers
                    .Where(er => er.EventId != null &&
                                 userGroupIds.Contains(er.GroupId ?? 0) &&
                                 _context.MstEvents.Any(e => e.EventId == er.EventId))
                    .Select(er => er.EventId.Value)
                    .Distinct()
                    .ToList();

                var query = _context.MstEvents
                    .Include(e => e.MstEventResponses)
                    .Include(e => e.MstLikesConfigs)
                    .Where(e =>
                        validEventIds.Contains(e.EventId) &&
                        e.FacilityId == facilityId &&
                        e.CreatedBy != userId &&
                        e.IsActive);

                // Filter by search title if provided
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(e => e.Title.Contains(search));
                }

                // Date/time filters based on dateFilter string
                DateTime today = DateTime.Today;
                DateTime startOfWeek = today.AddDays(-(int)today.DayOfWeek);  // Sunday as start
                DateTime endOfWeek = startOfWeek.AddDays(7).AddSeconds(-1);
                DateTime startOfNextMonth = new DateTime(today.Year, today.Month, 1).AddMonths(1);
                DateTime endOfNextMonth = startOfNextMonth.AddMonths(1).AddSeconds(-1);

                switch (dateFilter)
                {
                    case EventDateFilter.Today:
                        query = query.Where(e => e.EventDateTime.HasValue && e.EventDateTime.Value.Date == today);
                        break;

                    case EventDateFilter.Tomorrow:
                        var tomorrow = today.AddDays(1);
                        query = query.Where(e => e.EventDateTime.HasValue && e.EventDateTime.Value.Date == tomorrow);
                        break;

                    case EventDateFilter.ThisWeek:
                        query = query.Where(e => e.EventDateTime.HasValue && e.EventDateTime.Value >= startOfWeek && e.EventDateTime.Value <= endOfWeek);
                        break;

                    case EventDateFilter.ThisWeekend:
                        DateTime saturday = startOfWeek.AddDays(6);
                        DateTime sunday = startOfWeek.AddDays(7);
                        query = query.Where(e => e.EventDateTime.HasValue &&
                                                (e.EventDateTime.Value.Date == saturday.Date || e.EventDateTime.Value.Date == sunday.Date));
                        break;

                    case EventDateFilter.ThisMonth:
                        var startOfMonth = new DateTime(today.Year, today.Month, 1);
                        var endOfMonth = startOfMonth.AddMonths(1).AddSeconds(-1);
                        query = query.Where(e => e.EventDateTime.HasValue && e.EventDateTime.Value >= startOfMonth && e.EventDateTime.Value <= endOfMonth);
                        break;

                    case EventDateFilter.NextMonth:
                        query = query.Where(e => e.EventDateTime.HasValue && e.EventDateTime.Value >= startOfNextMonth && e.EventDateTime.Value <= endOfNextMonth);
                        break;

                    case EventDateFilter.None:
                    default:
                        // No date filter applied
                        break;
                }



                var events = query.ToList();

                var eventDtos = _mapper.Map<List<MstEventDto>>(events);

                var result = eventDtos.Select(dto =>
                {
                    var originalEvent = events.FirstOrDefault(e => e.EventId == dto.EventId);
                    int likeCount = originalEvent?.MstLikesConfigs?.Count ?? 0;
                    bool isLikedByUser = originalEvent?.MstLikesConfigs?.Any(like => like.UserId == userId) ?? false;
                    bool isAccepted = originalEvent?.MstEventResponses?
         .FirstOrDefault(r => r.UserId == userId)
         ?.IsAccepted ?? false;

                    return new
                    {
                        dto.EventId,
                        dto.Title,
                        dto.Description,
                        dto.MediaUrl,
                        dto.EventDateTime,
                        dto.Location,
                        dto.ExternalLink,
                        dto.CreatedAt,
                        dto.CreatedBy,
                        dto.FacilityId,
                        LikeCount = likeCount,
                        IsLikedByUser = isLikedByUser,
                        IsAcceptedByUser = isAccepted,
                        dto.MstEventResponses,
                        dto.IsRsvp
                    };
                }).ToList();

                return ApiResponseDto<object>.SuccessResponse(AppMessages.EventRetrieved, StatusCodes.Status200OK, result);
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetEventsByFacilityId] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }

        public ApiResponseDto<object> SaveEventResponse(MstEventResponseDto responseDto)
        {
            try
            {
                MstEventResponse entity;

                // Check if the response already exists for the same EventId and UserId
                entity = _context.MstEventResponses
                    .FirstOrDefault(e => e.EventId == responseDto.EventId && e.UserId == responseDto.UserId);

                if (entity != null)
                {
                    // Update existing response
                    entity.IsAccepted = responseDto.IsAccepted;
                    entity.RespondedAt = DateTime.UtcNow;
                    _context.MstEventResponses.Update(entity);
                }
                else
                {
                    // Insert new response
                    entity = _mapper.Map<MstEventResponse>(responseDto);
                    entity.RespondedAt = DateTime.UtcNow;
                    _context.MstEventResponses.Add(entity);
                }

                _context.SaveChanges();
                return ApiResponseDto<object>.SuccessResponse(
                    AppMessages.EventResponseSaved,
                    StatusCodes.Status200OK,
                    _mapper.Map<MstEventResponseDto>(entity)
                );
            }
            catch (Exception ex)
            {
                _logger.Error($"[SaveEventResponse] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(
                    AppMessages.InternalServerError,
                    StatusCodes.Status500InternalServerError
                );
            }
        }


        public ApiResponseDto<object> GetEventResponsesByEventId(int eventId)
        {
            try
            {
                var responses = _context.MstEventResponses.Where(r => r.EventId == eventId).ToList();
                var responseDtos = _mapper.Map<List<MstEventResponseDto>>(responses);
                return ApiResponseDto<object>.SuccessResponse(AppMessages.EventResponseRetrieved, StatusCodes.Status200OK, responseDtos);
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetEventResponsesByEventId] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }

        public ApiResponseDto<object> GetEventRsvpDetails(int eventId)
        {
            try
            {
                var responses = _context.MstEventResponses.Include(x=>x.User).Where(r => r.EventId == eventId).ToList();
                var responseDtos = responses.Select(r => new 
                {
                    UserId = r.User.UserId,
                    UserName = r.User.Username ?? r.User.Email,
                }).ToList();
                return ApiResponseDto<object>.SuccessResponse(AppMessages.EventResponseRetrieved, StatusCodes.Status200OK, responseDtos);
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetEventRsvpDetails] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }

        public ApiResponseDto<object> SaveEventLike(int eventId, int userId)
        {
            try
            {
                var existingLike = _context.MstLikesConfigs.FirstOrDefault(x => x.EventId == eventId && x.UserId == userId);
                if (existingLike != null)
                {
                    existingLike.IsLiked = !existingLike.IsLiked;
                    existingLike.LikedAt = DateTime.UtcNow;
                    _context.MstLikesConfigs.Update(existingLike);
                    _context.SaveChanges();
                    return ApiResponseDto<object>.SuccessResponse(AppMessages.LikeUpdated, StatusCodes.Status200OK, existingLike.IsLiked);
                }
                else
                {
                    var like = new MstLikesConfig
                    {
                        EventId = eventId,
                        UserId = userId,
                        IsLiked = true,
                        LikedAt = DateTime.UtcNow
                    };
                    _context.MstLikesConfigs.Add(like);
                    _context.SaveChanges();
                    return ApiResponseDto<object>.SuccessResponse(AppMessages.LikeSaved, StatusCodes.Status200OK, true);
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"[SaveEventLike] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }

        public ApiResponseDto<object> GetEventLikesByEventId(int eventId)
        {
            try
            {
                var likes = _context.MstLikesConfigs.Where(l => l.EventId == eventId && l.IsLiked).ToList();
                return ApiResponseDto<object>.SuccessResponse(AppMessages.LikeRetrieved, StatusCodes.Status200OK, likes.Count);
            }
            catch (Exception ex)
            {
                _logger.Error($"[GetEventLikesByEventId] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> ActivateEventAsync(int eventId)
        {
            try
            {
                var entity = await _context.MstEvents.FindAsync(eventId);
                if (entity == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.EventNotFound, StatusCodes.Status404NotFound);
                }
                if (entity.IsActive)
                {
                    return ApiResponseDto<object>.SuccessResponse(AppMessages.EventAlreadyActive, StatusCodes.Status200OK);
                }
                entity.IsActive = true;
                _context.MstEvents.Update(entity);
                await _context.SaveChangesAsync();
                return ApiResponseDto<object>.SuccessResponse(AppMessages.EventActivatedSuccessfully, StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                _logger.Error($"[ActivateEventAsync] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }

        public async Task<ApiResponseDto<object>> DeactivateEventAsync(int eventId)
        {
            try
            {
                var entity = await _context.MstEvents.FindAsync(eventId);
                if (entity == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.EventNotFound, StatusCodes.Status404NotFound);
                }
                if (!entity.IsActive)
                {
                    return ApiResponseDto<object>.SuccessResponse(AppMessages.EventAlreadyInactive, StatusCodes.Status200OK);
                }
                entity.IsActive = false;
                _context.MstEvents.Update(entity);
                await _context.SaveChangesAsync();
                return ApiResponseDto<object>.SuccessResponse(AppMessages.EventDeactivatedSuccessfully, StatusCodes.Status200OK);
            }
            catch (Exception ex)
            {
                _logger.Error($"[DeactivateEventAsync] Exception occurred - {ex.Message}");
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError);
            }
        }
        public ApiResponseDto<object> ToggleEventActivation(int eventId)
        {
            try
            {
                var entity = _context.MstEvents.Find(eventId);
                if (entity == null)
                {
                    return ApiResponseDto<object>.ErrorResponse(AppMessages.EventNotFound, StatusCodes.Status404NotFound, new List<Error>
                    {
                        new Error { Code = 404, Message = AppMessages.EventNotFound }
                    });
                }

                // Toggle the IsActive status
                entity.IsActive = !entity.IsActive;
                _context.SaveChanges();

                var message = entity.IsActive ? AppMessages.EventActivatedSuccessfully : AppMessages.EventDeactivatedSuccessfully;
                return ApiResponseDto<object>.SuccessResponse(message, StatusCodes.Status200OK, entity.IsActive);
            }
            catch (Exception ex)
            {
                return ApiResponseDto<object>.ErrorResponse(AppMessages.InternalServerError, StatusCodes.Status500InternalServerError, new List<Error>
                {
                    new Error { Code = 500, Message = ex.Message }
                });
            }
        }
    }
} 